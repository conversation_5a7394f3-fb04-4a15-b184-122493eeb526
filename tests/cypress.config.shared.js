const { defineConfig } = require("cypress");
// eslint-disable-next-line import/no-unresolved -- Correct Import is used
const { allureCypress } = require("allure-cypress/reporter");
const { SecretsManagerClient, GetSecretValueCommand } = require("@aws-sdk/client-secrets-manager");
const fs = require("fs");
const { verifyDownloadTasks } = require("cy-verify-downloads");
const grepPlugin = require("@cypress/grep/src/plugin");
const { loadFeeds } = require("./feeds");
const S3Service = require("./cypress/support/aws-client");
const localCredentials = require("./cypress/fixtures/local_credentials");
const path = require("path");
const { MWAAClient, InvokeRestApiCommand } = require("@aws-sdk/client-mwaa");

const generateUniqueBucketName = (baseName) => {
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 8);
    return `${baseName}-${randomString}-${timestamp}`;
};

async function streamToString(stream) {
    return new Promise((resolve, reject) => {
        const chunks = [];
        stream.on("data", (chunk) => {
            chunks.push(typeof chunk === "string" ? Buffer.from(chunk) : chunk);
        });
        stream.on("end", () => {
            resolve(Buffer.concat(chunks).toString("utf-8"));
        });
        stream.on("error", reject);
    });
}

const uniqueBucketName = generateUniqueBucketName("databee-cypress");

module.exports = defineConfig({
    reporter: "mocha-junit-reporter",
    reporterOptions: {
        testCaseSwitchClassnameAndName: true,
    },
    env: {
        ENVIRONMENT: "local",
        AWS_REGION: "us-east-1",
        AWS_ACCESS_KEY_ID: process.env.AWS_ACCESS_KEY_ID,
        AWS_SECRET_ACCESS_KEY: process.env.AWS_SECRET_ACCESS_KEY,
        AWS_SESSION_TOKEN: process.env.AWS_SESSION_TOKEN,
        S3_BUCKET_NAME: uniqueBucketName,
        TEST_TYPE: "SnowFlake",
        ENTITLEMENTS: ["BeeKeeper", "Security Hygiene", "Security Threats"],
        INSTALL_TYPE: {
            SSO: true,
        },
        grepFilterSpecs: true,
        grepOmitFiltered: true,
    },
    defaultCommandTimeout: 120000,
    pageLoadTimeout: 180000,
    execTimeout: 420000,
    viewportHeight: 768,
    viewportWidth: 1200,
    chromeWebSecurity: false,
    watchForFileChanges: false,
    retries: process.env.TEST_IN_WORKFLOW ? 1 : 0,
    screenshotsFolder: "cypress/screenshots",
    experimentalInteractiveRunEvents: true,
    experimentalMemoryManagement: true,
    numTestsKeptInMemory: 0,
    e2e: {
        // We've imported your old cypress plugins here.
        // You may want to clean this up later by importing these.
        async setupNodeEvents(on, config) {
            grepPlugin(config);
            allureCypress(on);
            on("task", verifyDownloadTasks);
            const params = {
                Bucket: config.env.S3_BUCKET_NAME,
            };

            // Create an instance of S3Service with the s3Client and params

            const s3Service = new S3Service(config.env.AWS_REGION, params);

            on("after:run", async (results) => {
                function writeToPropertiesFile(filePath, keyValuePairs) {
                    const propertiesString = Object.entries(keyValuePairs)
                        .map(([key, value]) => `${key}=${value}`)
                        .join("\n");

                    fs.writeFileSync(filePath, propertiesString, "utf-8");
                }
                const propertiesFilePath = "allure-results/environment.properties";
                const keyValuePairs = {
                    BROWSER_NAME: results.browserName.toUpperCase(),
                    BROWSER_VERSION: results.browserVersion,
                    OS_NAME: results.osName.toUpperCase(),
                    OS_VERSION: results.osVersion,
                    CYPRESS_VERSION: results.cypressVersion,
                    CLUSTER: results.config.env.credentials.CLUSTER_UI.split(".")[0].substring(8) || "Not Available",
                    TENANT: results.config.env.credentials.DATABEE_UI.split(".")[0].split("//")[1],
                };
                writeToPropertiesFile(propertiesFilePath, keyValuePairs);

                const resultsJson = path.resolve(__dirname, "cypress/common/teams/teamsData.json");
                fs.writeFileSync(resultsJson, JSON.stringify(results, null, 2), "utf-8");

                // it will delete the existing buckets.
                await s3Service.deleteS3Bucket();
            });
            // Below the function to connect the AWS secrets manager
            // And will retrieve the credentials from the AWS and stored in config variables
            async function getAWSSecrets() {
                const client = new SecretsManagerClient({
                    region: config.env.AWS_REGION,
                });

                const response = await client.send(
                    new GetSecretValueCommand({
                        SecretId: `cypress_test/${config.env.ENVIRONMENT}/env`,
                        VersionStage: "AWSCURRENT",
                    })
                );

                const secret = response.SecretString;
                return JSON.parse(secret);
            }

            const tempConfig = config;

            // Loading feeds for data collector flow
            const { collectorName, os } = config.env;
            if (collectorName) {
                tempConfig.feeds = await loadFeeds(collectorName, os);
            }

            if (config.env.ENVIRONMENT === "local_creds") {
                tempConfig.env.credentials = localCredentials;
            } else {
                tempConfig.env.credentials = await getAWSSecrets();
            }

            try {
                await s3Service.createS3Bucket();
            } catch (error) {
                return { error };
            }

            on("task", {
                createBucket() {
                    return s3Service.createS3Bucket();
                },

                createS3EventNotification(SqsQueueConfig) {
                    return s3Service.createEventNotification(SqsQueueConfig).then((SqsQueueConfigResult) => {
                        if (SqsQueueConfigResult.error) {
                            throw new Error(
                                `createS3EventNotification Error Message: ${SqsQueueConfigResult.error.message}`
                            );
                        }
                        return SqsQueueConfig;
                    });
                },
                deleteS3BucketPolicy() {
                    return s3Service.deleteBucketPolicyPermissions().then((policyDeleteResult) => {
                        if (policyDeleteResult.error) {
                            throw new Error(`deleteS3BucketPolicy Error Message: ${policyDeleteResult.error.message}`);
                        }
                        return policyDeleteResult;
                    });
                },
                putS3BucketPolicy(policy) {
                    return s3Service.addBucketPolicyPermissions(policy).then((policyResult) => {
                        if (policyResult.error) {
                            throw new Error(`putS3BucketPolicy Error Message: ${policyResult.error.message}`);
                        }
                        return policyResult;
                    });
                },
                deleteS3EventNotification(notificationId) {
                    return s3Service.deleteEventNotification(notificationId).then((notificationDeleteResult) => {
                        if (notificationDeleteResult.error) {
                            throw new Error(
                                `deleteS3EventNotification Error Message: ${notificationDeleteResult.error.message}`
                            );
                        }
                        return notificationDeleteResult;
                    });
                },
                // To call the task from the test file, you should pass an array of files as source files.
                /* const filesToUpload = [
                        "/path/to/your/file1.extension",
                        "/path/to/your/file2.extension",
                        "/path/to/your/file3.extension",
                    ]; */
                uploadFilesToS3Bucket(filesToUpload) {
                    return s3Service.uploadFileToS3(filesToUpload).then((filesUploadResult) => {
                        if (filesUploadResult.error) {
                            throw new Error(`filesUploadResult Error Message: ${filesUploadResult.error.message}`);
                        }
                        return filesUploadResult;
                    });
                },
                // To call the task from the test file, you should pass an array of files name as objects.
                /* const filesToDelete = [
                        "file1.extension",
                        "file2.extension",
                        "file3.extension",
                    ]; */
                deleteFilesFromS3(filesToDelete) {
                    return s3Service.deleteFilesFromS3Bucket(filesToDelete).then((deleteFileResult) => {
                        if (deleteFileResult.error) {
                            throw new Error(`deleteFileResult Error Message: ${deleteFileResult.error.message}`);
                        }
                        return deleteFileResult;
                    });
                },
                getFilesFromS3Bucket(prefix) {
                    return s3Service.getFilesFromS3(prefix).then((getFileResult) => {
                        if (getFileResult.error) {
                            throw new Error(`getFileResult Error Message: ${getFileResult.error.message}`);
                        }
                        return getFileResult;
                    });
                },
                async invokeAirflowApi({ Name, Method = "GET", Path, Region = config.env.AWS_REGION }) {
                    try {
                        const client = new MWAAClient({ Region });
                        const encodedPath = encodeURI(Path);
                        const command = new InvokeRestApiCommand({
                            Name,
                            Method,
                            Path: encodedPath,
                        });

                        const response = await client.send(command);
                        try {
                            const parsed = JSON.parse(response.RestApiResponse);
                            return parsed;
                        } catch (e) {
                            return response.RestApiResponse;
                        }
                    } catch (err) {
                        if (err?.$response?.body) {
                            const raw =
                                (await err.$response.body.transformToString?.()) ||
                                (await streamToString(err.$response.body));
                            return { error: err, rawResponse: raw };
                        }
                        throw err;
                    }
                },
            });
            return tempConfig;
        },
        excludeSpecPattern: [
            "**/examples/*",
            "cypress/e2e/ui/cluster/content_delivery/content_delivery.cy.js",
            "cypress/e2e/ui/iris/content_delivery/content_delivery.cy.js",
            "cypress/e2e/ui/iris/navigation_rbac/remediation_actions.cy.js",
        ],
        supportFile: "cypress/support/e2e.js",
        specPattern: [
            "cypress/e2e/ui/iris/configuration/data_lakes.cy.js",
            "cypress/e2e/ui/shared/configuration/entitlements.cy.js",
            "cypress/e2e/ui/cyber_ui/configuration/active_detections.cy.js",
            "cypress/e2e/ui/iris/data_feed/aws_s3_feed_validation.cy.js",
            "cypress/e2e/ui/iris/data_feed/fetchDataFeedConfigurations.cy.js",
            "cypress/e2e/**/*!(data_lakes|entitlements|active_detections|data_source|fetchDataFeedConfigurations).cy.js",
        ],
    },
});
