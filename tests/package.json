{"name": "cypress-tests", "description": "Iris web application integration test suite.", "license": "Comcast Proprietary", "private": true, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["defaults", "last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "scripts": {"prepare": "cd .. && husky install tests/.husky", "cypress:run": "cypress run --browser chrome --config-file cypress.config.shared.js", "clean:report": "rm -R -f allure-report/* && rm -R -f allure-results/*", "gen:report": "allure generate allure-results --clean --single-file -o allure-report", "open:report": "allure open allure-report", "cypress:local": "cypress open --env ENVIRONMENT=local_creds --config-file cypress.config.shared.js", "cypress:open": "cypress open --browser chrome --config-file cypress.config.shared.js", "cypress:test": "yarn cypress:run ; yarn gen:report", "teams:report": "yarn node apiApp.js", "lint": "eslint . --ext .js --ext .jsx", "lint:fix": "eslint . --fix --ext .js --ext .jsx"}, "prettier": "@bluvector/eslint-config/prettierrc", "lint-staged": {"**/*.+(js|jsx)": ["eslint --fix", "prettier --write"], "**/*.+(json|css|scss|md)": ["prettier --write"]}, "babel": {"presets": [], "plugins": []}, "devDependencies": {"@aws-sdk/client-s3": "^3.798.0", "@aws-sdk/client-secrets-manager": "^3.798.0", "@babel/core": "^7.26.10", "@babel/eslint-parser": "^7.27.0", "@babel/eslint-plugin": "^7.27.0", "@babel/plugin-transform-runtime": "^7.26.10", "@babel/preset-env": "^7.26.9", "@babel/preset-react": "^7.26.3", "@babel/runtime": "^7.27.0", "@bluvector/eslint-config": "^1.1.14", "@cypress/grep": "^3.1.5", "@eslint-community/eslint-plugin-eslint-comments": "^4.5.0", "@testing-library/cypress": "^10.0.3", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "allure-cypress": "^2.15.1", "babel-plugin-transform-import-meta": "^2.3.2", "cy-verify-downloads": "0.2.6", "cypress": "^14.3.3", "dotenv": "^16.5.0", "eslint": "^8.57.1", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.10.0", "eslint-plugin-cypress": "^2.15.2", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jest": "^27.9.0", "eslint-plugin-jest-dom": "^5.5.0", "eslint-plugin-jsx-a11y": "6.10.2", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-simple-import-sort": "^10.0.0", "eslint-plugin-testing-library": "^6.5.0", "husky": "^8.0.3", "js-yaml": "^4.1.0", "lint-staged": "^15.5.1", "mocha": "^10.8.2", "moment": "^2.30.1", "npm-run-all": "^4.1.5", "prettier": "^2.8.8", "react": "^18.3.1"}, "dependencies": {"@aws-sdk/client-mwaa": "^3.856.0", "@slack/webhook": "^7.0.5", "allure-commandline": "^2.34.0", "cron-parser": "^5.3.0", "mocha-junit-reporter": "^2.2.1"}, "packageManager": "yarn@4.3.1+sha512.af78262d7d125afbfeed740602ace8c5e4405cd7f4735c08feb327286b2fdb2390fbca01589bfd1f50b1240548b74806767f5a063c94b67e431aabd0d86f7774"}