const successColor = "Good";
const failedColor = "Attention";
const gitHubRunNumber = process.env.GITHUB_RUN_NUMBER;
const gitHubRunId = process.env.GITHUB_RUN_ID;
const gitHubUrl = process.env.GITHUB_URL;
const gitHubBuild = `${gitHubUrl}/actions/runs/${gitHubRunId}`;
const allureReportDir = process.env.GITHUB_STEP_ID
    ? `${gitHubRunNumber}-${process.env.GITHUB_STEP_ID}`
    : gitHubRunNumber;
const allureReportPath = `Cypress-Test-Results/${process.env.CYPRESS_ENVIRONMENT}/${process.env.REPORT_NAME}Allure-Report-${allureReportDir}`;
const allureReportPage = `${process.env.CLOUDFRONT_URL}/${allureReportPath}/index.html`;
const reportCardTitle = process.env.NOTIFICATION_CARD_TITLE ?? "E2E Cypress Tests";

class TeamsMessageBuilder {
    build(results) {
        const failed = results.totalFailed > 0;
        const result = failed ? "Failed" : "Passed";
        const color = failed ? failedColor : successColor;

        let buildLink;
        let buildURL;
        let allureLink;
        let allureURL;
        const clusterUrl = results.config.env.credentials.CLUSTER_UI.split(".")[0].substring(8) || "Not Available";
        const databeeUrl = results.config.env.credentials.DATABEE_UI.split(".")[0].split("//")[1] || "Not Available";

        if (gitHubRunId !== undefined) {
            buildLink = `View Build #${gitHubRunNumber}`;
            buildURL = gitHubBuild;
            allureLink = "View Allure Report";
            allureURL = allureReportPage;
        } else {
            buildLink = "No Build";
            buildURL = "";
            allureLink = "Report Generated";
            allureURL = "";
        }

        this.messageBody = {
            type: "message",
            attachments: [
                {
                    contentType: "application/vnd.microsoft.card.adaptive",
                    content: {
                        type: "AdaptiveCard",
                        body: [
                            {
                                type: "TextBlock",
                                size: "extraLarge",
                                weight: "bolder",
                                text: `Test Suite: ${reportCardTitle}`,
                                style: "heading",
                                wrap: true,
                            },
                            {
                                type: "ColumnSet",
                                columns: [
                                    {
                                        type: "Column",
                                        items: [
                                            {
                                                type: "ColumnSet",
                                                columns: [
                                                    {
                                                        type: "Column",
                                                        width: "auto",
                                                        items: [
                                                            {
                                                                type: "TextBlock",
                                                                weight: "bolder",
                                                                text: "Build Results: ",
                                                                wrap: true,
                                                                size: "medium",
                                                            },
                                                        ],
                                                    },
                                                    {
                                                        type: "Column",
                                                        width: "auto",
                                                        items: [
                                                            {
                                                                type: "TextBlock",
                                                                text: result,
                                                                wrap: true,
                                                                color,
                                                                size: "medium",
                                                            },
                                                        ],
                                                    },
                                                ],
                                            },
                                        ],
                                        width: "stretch",
                                    },
                                ],
                            },
                            {
                                type: "ColumnSet",
                                columns: [
                                    {
                                        type: "Column",
                                        width: "stretch",
                                        items: [
                                            {
                                                type: "FactSet",
                                                facts: [
                                                    {
                                                        title: "Number of Tests:",
                                                        value: results.totalTests,
                                                    },
                                                    {
                                                        title: "Failed:",
                                                        value: results.totalFailed,
                                                    },
                                                    {
                                                        title: "Passed:",
                                                        value: results.totalPassed,
                                                    },
                                                    {
                                                        title: "Pending:",
                                                        value: results.totalPending,
                                                    },
                                                    {
                                                        title: "Skipped:",
                                                        value: results.totalSkipped,
                                                    },
                                                ],
                                            },
                                        ],
                                    },
                                    {
                                        type: "Column",
                                        width: "stretch",
                                        items: [
                                            {
                                                type: "FactSet",
                                                facts: [
                                                    {
                                                        title: "Browser:",
                                                        value: results.browserName.toUpperCase(),
                                                    },
                                                    {
                                                        title: "Version:",
                                                        value: results.browserVersion,
                                                    },
                                                    {
                                                        title: "Cluster:",
                                                        value: clusterUrl,
                                                    },
                                                    {
                                                        title: "Tenant:",
                                                        value: databeeUrl,
                                                    },
                                                ],
                                            },
                                        ],
                                    },
                                ],
                            },
                        ],
                        actions: [
                            {
                                type: "Action.OpenUrl",
                                title: buildLink,
                                url: buildURL,
                                role: "Button",
                            },
                            {
                                type: "Action.OpenUrl",
                                title: allureLink,
                                url: allureURL,
                                role: "Button",
                            },
                        ],
                        $schema: "http://adaptivecards.io/schemas/adaptive-card.json",
                        version: "1.6",
                    },
                },
            ],
        };

        return this.messageBody;
    }
}

module.exports = TeamsMessageBuilder;
