[{"sourcename": "1password", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Abnormal Security", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Action1 Patch Management", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "ADManager Plus", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Akamai CPC", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Akamai Noname API Security", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Akamai WAF", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "alicloud", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "An<PERSON><PERSON> ThreatStream", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Ansible AWX", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Apache HTTP Server", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Aqua CSPM", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Aqua Workload Protection", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "<PERSON><PERSON>", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Atlassian Confluence", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "AttackIQ", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Auth0", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "AWS Cloudfront", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "AWS Cloudtrail", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "AWS ELB", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "AWS GuardDuty Findings", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "<PERSON><PERSON>", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "AWS Route 53", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "AWS Security Hub", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "AWS VPC", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "AWS WAF", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "AWS Workspaces", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Azure Activity", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Azure AD", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Azure AD Sign-in", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Azure Audit", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Azure Front Door", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Azure Functions", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Azure Managed SQL", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Azure Resource Logs", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Azure Tenant Activity", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Azure VDI", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Azure WAF", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "BambooHR", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Barracuda Email Gateway Defense", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Better Mobile Threat Defense", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "BeyondTrust Password Safe", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "BeyondTrust Privilege Remote Access", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "BitSight", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Bitwarden", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "BlackBerry CylancePROTECT", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "BluVector", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "BMC Helix ITSM", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Box", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Carbon Black", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Censys", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Checkpoint Firewall", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Cisco Armorblox", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Cisco ASA", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "<PERSON><PERSON>", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Cisco Duo", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Cisco Email Security", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Cisco Firepower", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Cisco ISE", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "<PERSON><PERSON>", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Cisco Secure Email Cloud Gateway", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Cisco Secure Endpoint", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "<PERSON><PERSON>", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Citrix ADC", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Citrix WAF", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Claroty CTD", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Cloud Foundry", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Cloudflare Access", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Cloudflare DNS", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Cloudflare WAF", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Cofense Intelligence", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Cofense Triage", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Cohesity", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Contrast Security", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Cosmos Continuous Penetration Testing", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "CrowdStrike", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "CrowdStrike Device", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "CyberArk EPM", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Cyberark Identity", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "CyberArk Privilege Cloud", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "CyberArk Privileged Access Manager", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Cyberark Vault", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "CybeReady", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Cybereason", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "CybSafe", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Cynet", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Darktrace", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Darktrace / EMAIL", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Databricks", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Dataminr", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Delinea Privilege Manager", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Dragos Platform", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Dragos WorldView", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Dropbox", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Dynatrace Application Security", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Entrust", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "ExtraHop Reveal(X) 360", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "F5 BIG-IP", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Application Security Manager", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Local Traffic Manager", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Network Firewall", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "SSL VPN", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Fastly", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "FireEye EDR", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "<PERSON><PERSON>", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Forcepoint DLP", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Forcepoint Web Security", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Forescout eyeInspect", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Forescout eyeSight", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Forgerock identity cloud", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Fortify By OpenText", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Fortinet Firewall", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Fortinet UTM Device", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "GitHub Dependabot", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "GitHub Enterprise Cloud Audit", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Gitlab Compliance Center", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Gitlab Security", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "GitLab Source Code Control", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Google Cloud Identity", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Google Directory", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Google Security Command Center", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Greynoise", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Halcyon Anti Ransomware", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "HashiCorp Vault", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Have I Been Pwned", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "HCL AppScan", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "HCL BigFix", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "HPE Aruba Networking", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "IBM Security Guardium", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Illumio Core", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Imperva Secure Sphere", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Infoblox BloxOne Threat Defense", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Infoblox DDI", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Informatica", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "ISC Bind", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "<PERSON>ti CMDB", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Ivanti Endpoint Management", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "<PERSON><PERSON> Manager", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Jamf Protect", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "JFrog Platform", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Jfrog Xray", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Atlassian Jira", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Jumpcloud", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Juniper SRX", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "<PERSON><PERSON><PERSON>", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Kandji <PERSON>", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Kiteworks", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "KnowBe4", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Lacework", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Lansweeper", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "LimaCharlie", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Linux", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Log Analytics Azure Activity", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Log Analytics XDR", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "LogicMonitor Config Manager", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "ManageEngine MDM", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "ManageEngine ServiceDesk Plus", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Microsoft Defender for Endpoint Software Inventory", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Microsoft Active Directory", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Microsoft AMA Log Analytics", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Microsoft Defender EASM", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Microsoft Defender For Cloud", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Microsoft Defender For Cloud Apps", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Microsoft Defender For Cloud FIM", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Microsoft Defender for Endpoint", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Microsoft Defender For Identity", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Microsoft Defender for Office 365", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Microsoft Defender for Office 365 Audits", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Microsoft Entra", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Microsoft Entra MFA", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Microsoft IIS", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Microsoft Intune", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Microsoft SCCM", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Microsoft Sentinel", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Microsoft Sysmon", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Microsoft XDR", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Mimecast Audit and Authentication", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Mimecast Awareness Training", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Mimecast Email Security", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Microsoft Defender for Office 365 E-Discovery Cases", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Netskope", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "NetWitness", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Netwrix Auditor", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "NIST CVE", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Okta SSO", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "OneLogin", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "OneTrust Privacy Automation", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "OneTrust Third Party Management", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "OpenVPN Server", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Oracle Identity Governance", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Orca Security", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Osirium Privileged Access Management", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "OttoJS", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Palo Alto Cortex XDR", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Palo Alto Networks Firewall", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Palo Alto Prisma Cloud", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "pfSense", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Picus Security", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Ping Federate", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Ping One", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Pleasant Password Server", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Postgresql Events", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Progress Chef", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Proofpoint On Demand", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Proofpoint TAP", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Proofpoint Zenguide", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "<PERSON><PERSON><PERSON>", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Qualys Policy Compliance", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Qualys VM Report", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Qualys Vulnerability Management", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Rapid7 InsightVM", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Rapid7 Nexpose Vulnerabilities Scanner", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "RSA SecurId Authentication Manager", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Sailpoint IdentityIQ", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Sailpoint IdentityNow", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Salesforce", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "SAP SuccessFactors", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Saviynt Enterprise Identity", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Securonix", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "SentinelOne", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "ServiceNow CMDB", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "ServiceNow Policy Exception", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "ServiceNow SIR", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "ServiceNow Tickets", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "ServiceNow Vulnerability", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "<PERSON><PERSON>ck", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "<PERSON>nort", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Snyk Application Security", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Solarwinds Orion", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Sonar Cloud", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "SonarQube", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Sonicwall Firewall", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Sonrai Security", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Sophos Central Alerts", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Sophos Endpoint Protection", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "SOTI MobiControl", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "<PERSON><PERSON><PERSON><PERSON>", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Splunk Notables", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Squid Proxy", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Symantec Bluecoat ProxySG", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Symantec DLP", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Symantec Endpoint", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Sysdig", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Tangoe Wireless MDM", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Tanium Endpoint Protection", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Tenable Security Center", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Tenable Vulnerability Management", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Terraform", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Thinkst Canary", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "ThreatX WAF", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Thycotic Secret Server", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Trellix ePO", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Trend Micro Cloud One", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Trend Micro Deep Security", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Trend Micro Vision One", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "UpGuard", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Vectra Detect", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Vectra XDR", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "<PERSON><PERSON><PERSON>", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Veracode", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "VMWare ESXi", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "VMWare UAG", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "VMware vCenter", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Whistic", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Windows DNS Logs", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Windows Event Log", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Windows Processes", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "WithSecure Elements", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Wiz", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Workday", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Workday HR", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Workday Learning", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Zeek", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Zendesk", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Zerofox", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Zimperium MTD", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Zscaler Client Connector", "configurations": {"option1": "API Ingest", "option2": "Azure Blob", "option3": "Data Collector", "option4": "HTTP Collector", "option5": "AWS S3"}}, {"sourcename": "Zscaler Internet Access", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}, {"sourcename": "Zscaler Private Access", "configurations": {"option1": "Azure Blob", "option2": "Data Collector", "option3": "HTTP Collector", "option4": "AWS S3"}}]