// Conten Delivery Utils

export function goToContentDelivery(biTool = "Tableau") {
    cy.navigateTopNav("Compliance");
    cy.findByText(biTool).click();
}

export const USER_ROLES = {
    admin: {
        username: Cypress.env("credentials").CLUSTER_USER_IDS.ADMIN,
        password: Cypress.env("credentials").CLUSTER_ADMIN_PASSWORD,
    },
    analyst: {
        username: Cypress.env("credentials").DATABEE_USER_IDS.ANALYST,
        password: Cypress.env("credentials").DATABEE_ANALYST_PASSWORD,
    },
    data_engineer: {
        username: Cypress.env("credentials").DATABEE_USER_IDS.DATAENGINEER,
        password: Cypress.env("credentials").DATABEE_DATAENGINEER_PASSWORD,
    },
};

const requestDashboard = (dashboardName, force = false) => {
    cy.findAllByLabelText("Search").click().clear();
    cy.findAllByLabelText("Search").click().type(dashboardName);
    cy.findByText(dashboardName).should("be.visible");

    cy.contains(".card", dashboardName).then(($control) => {
        if ($control.find("button").text().includes("Contact DataBee to Purchase") || force) {
            // If dashboard is not requested, click the "Contact DataBee to Purchase" button
            cy.wrap($control).within(() => {
                cy.findByRole("button", { name: "Contact DataBee to Purchase" }).click();
                cy.findByText("Report Requested").should("be.visible");
            });
        } else {
            // If dashboard is requested, do nothing
            cy.log(`${dashboardName} dashboard is already requested. No action needed.`);
        }
    });
};

const purchaseDashboard = (dashboardNames, force = false) => {
    const dashboards = Array.isArray(dashboardNames) ? dashboardNames : [dashboardNames];

    cy.contains("label", "Purchased Reports")
        .parent()
        .find(".react-select__control")
        .then(($control) => {
            const controlText = $control.text();

            const shouldAdd = dashboards.some((name) => !controlText.includes(name) || force);

            if (shouldAdd) {
                cy.wrap($control)
                    .should("exist")
                    .should("be.visible")
                    .click({ force: true })
                    .then(() => {
                        dashboards.forEach((dashboardName) => {
                            cy.get(".react-select__menu")
                                .should("be.visible")
                                .contains(".react-select__option", dashboardName)
                                .click();
                        });
                    });

                // Click outside to close dropdown
                cy.findByText("Content Management").click({ force: true });

                // Submit the form
                cy.findByRole("button", { name: "Submit" }).click();
                cy.contains("Tenant details updated.").should("be.visible");
            } else {
                cy.log("All specified dashboards are already purchased. No action needed.");
            }
        });
};

const removePurchasedDashboard = () => {
    cy.contains("label", "Purchased Reports")
        .parent()
        .find(".react-select__control")
        .then(($control) => {
            const clearIndicator = $control.find(".react-select__clear-indicator");
            if (clearIndicator.length > 0) {
                cy.wrap(clearIndicator).click({ force: true });
                // Click outside to close dropdown
                cy.findByText("Content Management").click({ force: true });

                // Submit the form
                cy.findByRole("button", { name: "Submit" }).click();
                cy.contains("Tenant details updated.").should("be.visible");
            } else {
                cy.log("None of the specified dashboards are currently purchased. No action needed.");
            }
        });
};

const cancelDashboardRequest = (dashboardName, force = false) => {
    cy.get("body").then(($body) => {
        // Check if dashbord exists in the list
        if ($body.find(`.list-group-item:contains(${dashboardName})`).length > 0 || force) {
            // If found, click the "Cancel" button
            cy.contains(".list-group-item", dashboardName).within(() => {
                cy.findByRole("button", { name: "Cancel" }).click();
                cy.findByText("Pending Cancel").should("be.visible");
            });

            // Submit the form
            cy.findByRole("button", { name: "Submit" }).click();
            cy.contains("Tenant details updated.").should("be.visible");
        } else {
            // If dashboard is not found, do nothing
            cy.log(`${dashboardName} is not present. No action needed.`);
        }
    });
};

const setDeliveryType = (deliveryType) => {
    cy.contains("label", "Delivery Type")
        .parent()
        .find(".css-1dimb5e-singleValue")
        .then(($selectedValue) => {
            const currentValue = $selectedValue.text();

            if (currentValue !== deliveryType) {
                cy.contains("label", "Delivery Type")
                    .parent()
                    .find("input")
                    .clear()
                    .type(deliveryType)
                    .type("{enter}{enter}");
            }
        });
};

export { cancelDashboardRequest, purchaseDashboard, removePurchasedDashboard, requestDashboard, setDeliveryType };
