/// <reference types="cypress"/>

describe(
    "Validate Data Quality, Feedside Panel Overview, and Data Catalog Navigation",
    { tags: ["demo_tests"] },
    () => {
        beforeEach(() => {
            cy.clearCookies();
            cy.visit(Cypress.env("credentials").DATABEE_UI);
            cy.manualLogin(
                Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
                Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
            );
        });

        it("should validate Feedside Panel & Data Quality Page", () => {
            cy.navigateTopNav("Console", "Overview");
            cy.contains(".dashboard-widget", "Feed Health (Past 1 Week)")
                .find("tbody tr")
                .first()
                .find("td")
                .first()
                .find("button")
                .then(($el) => {
                    cy.wrap($el)
                        .invoke("text")
                        .then((text) => {
                            cy.wrap(text).as("linkText");
                        });
                    cy.wrap($el).click();
                });
            cy.findByRole("dialog").within(() => {
                cy.get(".offcanvas-title")
                    .invoke("text")
                    .then((text) => {
                        cy.get("@linkText").then((linkTxt) => {
                            expect(text).to.equal(linkTxt);
                        });
                    });
                cy.findByRole("button", { name: "View Data Quality Summary" }).click();
            });
            cy.findByRole("heading", { level: 1 }).should("contain.text", "Data Quality");
            cy.get(".data-quality-card.card").within(() => {
                cy.findByRole("heading", { level: 3 })
                    .invoke("text")
                    .then((text) => {
                        cy.get("@linkText").then((linkTxt) => {
                            expect(text).to.equal(linkTxt);
                        });
                    });
            });
            cy.get("#data-path-diagram-legend [role='button']").should("have.length", 3);
            cy.get(".highcharts-container")
                .should("exist")
                .and("contain.text", "unmapped")
                .and("contain.text", "failed");
            cy.findByTitle("Data Feed Timeline").click();
            cy.findByRole("dialog")
                .should("exist")
                .within(() => {
                    cy.get(".alert.alert-danger.show").should("not.exist");
                    cy.findByRole("heading", { level: 4 })
                        .invoke("text")
                        .then((text) => {
                            cy.get("@linkText").then((linkTxt) => {
                                expect(text).to.contain(linkTxt);
                            });
                        });
                });
            cy.get("body").type("{esc}");
        });

        it("should validate Unprocessed Events table & Actions dialog", () => {
            cy.navigateTopNav("Data", "Unprocessed Events");
            cy.contains("Searching...").should("not.exist");
            cy.findByRole("heading", { level: 1 }).should("contain.text", "Unprocessed Events");
            cy.findByRole("table").find("th").as("tableHeaders");

            cy.get("@tableHeaders")
                .should("exist")
                .then(($headers) => {
                    const $actionsColumn = $headers.filter(":contains(Actions)");
                    expect($actionsColumn).to.have.length.at.least(1);
                    const actionsIndex = $actionsColumn.index();

                    const $feedNameColumn = $headers.filter(":contains(Feed Name)");
                    const feedNameIndex = $feedNameColumn.index();

                    cy.findByRole("table")
                        .find("tbody tr")
                        .first()
                        .find("td")
                        .then(($td) => {
                            cy.wrap($td)
                                .eq(feedNameIndex)
                                .invoke("text")
                                .then((text) => {
                                    cy.wrap(text).as("feedNameText");
                                });
                            cy.wrap($td)
                                .eq(actionsIndex)
                                .scrollIntoView()
                                .should("be.visible")
                                .within(() => {
                                    cy.findByRole("button").should("be.visible").click();
                                });
                        });
                });
            cy.findByRole("dialog")
                .should("exist")
                .within(() => {
                    cy.get(".alert.alert-danger.show").should("not.exist");
                    cy.findByRole("heading", { level: 5 })
                        .invoke("text")
                        .then((text) => {
                            cy.get("@feedNameText").then((feedNameTxt) => {
                                expect(text).to.equal(`${feedNameTxt} Details`);
                            });
                        });
                    cy.get(".col-md-6").each(($el, index) => {
                        cy.findAllByTitle("Copy Content").eq(index).should("exist").and("be.enabled");
                        cy.wrap($el.find("h4"))
                            .invoke("text")
                            .then((title) => {
                                if (title === "Processed Data") {
                                    cy.get(".pretty-json-container")
                                        .eq(index)
                                        .invoke("text")
                                        .then((text) => {
                                            cy.get("@feedNameText").then((feedNameTxt) => {
                                                expect(text).to.contain(feedNameTxt);
                                            });
                                        });
                                }
                            });
                    });
                });
            cy.get("body").type("{esc}");
            cy.findByRole("dialog").should("not.exist");
        });

        it("should validate Data Catalog Link and Link Navigation URL", { tags: ["demo_prod_only"] }, () => {
            cy.navigateTopNav("Console", "Overview");
            cy.findByTitle("Data Catalog").then(($dataCatalog) => {
                cy.wrap($dataCatalog)
                    .should("exist")
                    .invoke("attr", "href")
                    .then((text) => {
                        expect(text).to.contain("catalog");
                    });
            });
        });

        afterEach(() => {
            cy.clickLogout();
        });
    }
);
