/// <reference types="cypress"/>

import { eventTimelineSingleFilterValidation } from "../cyber_ui/entity_view/entity_utils";
import {
    pivotTableWidgetAndVerifyNavigation,
    validateAllWidgetsDisplayWithContent,
} from "../iris/dashboards/dashboard_utils";

describe("Lois Lane | Expected Content Displayed on Entity Details Page", { tags: ["demo_tests"] }, () => {
    const expectedDataBeeFindingsTableHeaders = [
        "Event Time (EDT)",
        "Confidence ID",
        "Device: hostname",
        "Finding Information: desc",
        "Finding Information: title",
        "Message",
        "Metadata: product.name",
        "Severity ID",
        "Status ID",
        "Type ID",
        "User: name",
        "Metadata: product.feature.name",
        "Finding Information: analytic.name",
        "Actions",
    ];

    const timelineEventsChecks = [
        ["File Hosting Activity", "file_hosting"],
        ["Detection Finding", "detection_finding"],
        ["Email Activity", "email_activity"],
        ["SMB Activity", "smb_activity"],
    ];

    beforeEach(() => {
        cy.clearCookies();
        cy.visit(Cypress.env("credentials").DATABEE_UI);
        cy.manualLogin(
            Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
            Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
        );
    });

    it("Validate | Widgets are displayed on Console Overview & Exposure Management Page", () => {
        cy.navigateTopNav("Console", "Overview");
        cy.retryTextNotContainCheck(".alert.alert-primary.fade.show", "Loading...");
        cy.findByText("Security Data Fabric - Health", { selector: ".dashboard-widget.card h5" }).should("be.visible");
        validateAllWidgetsDisplayWithContent();
        cy.navigateTopNav("Exposure", "Exposure Management");
        cy.retryTextNotContainCheck(".alert.alert-primary.fade.show", "Loading...");
        validateAllWidgetsDisplayWithContent();
    });

    it("Validate | Events Timeline, File Hosting Activity, Detection Finding, Email Activity & SMB Activity", () => {
        cy.navigateTopNav("Console", "Overview");
        pivotTableWidgetAndVerifyNavigation(
            "Highest Severity Findings From DataBee (Past 1 Week)",
            "Triggered 'Potential Insider Threat Detection Chain'"
        );
        cy.retryTextNotContainCheck(".alert.alert-info.fade.show", " Loading DataBee Findings...");
        cy.findByRole("heading", { name: "DataBee Findings" }).should("be.visible");

        // Validate that all table headers are visible, and the header text is displayed,
        // also validate that each column has a value displayed and not null or undefined
        cy.findByRole("table")
            .get("#search-results")
            .findAllByRole("columnheader")
            .each(($header, index) => {
                cy.wrap($header).should("have.text", expectedDataBeeFindingsTableHeaders[index]);
                cy.findByRole("table")
                    .get("#search-results")
                    .find("tbody")
                    .findByRole("row")
                    .findAllByRole("cell")
                    .eq(index)
                    .invoke("text")
                    .then((text) => {
                        if ($header.text() === "Status ID" || $header.text() === "Actions") {
                            expect(text.length).to.be.eq(0);
                        } else {
                            expect(text.length).to.be.greaterThan(1);
                            expect(text).not.to.be.eq("undefined");
                        }
                    });
            });

        // Validate the table shows just one row for the DataBee Findings,
        // also validate the user: name column has name lois and click on the link
        cy.findByRole("table")
            .get("#search-results")
            .find("tbody")
            .findAllByRole("row")
            .should("have.length", 1)
            .then(() => {
                cy.findByRole("table")
                    .get("#search-results")
                    .findByRole("columnheader", { name: "User: name" })
                    .invoke("index")
                    .then((index) => {
                        cy.get("#search-results")
                            .find("tbody")
                            .findByRole("row")
                            .findAllByRole("cell")
                            .eq(index)
                            .should("have.text", "lois185")
                            .find("a")
                            .scrollIntoView()
                            .click();
                    });
            });
        cy.retryTextNotContainCheck(".alert.alert-info.fade.show", " Loading... This might take several minutes");
        cy.findByRole("heading", { level: 1 }).should("contain.text", "Lois Lane").should("be.visible");
        cy.get("#event-history").within(() => {
            cy.findByRole("heading", { level: 4 }).should("have.text", "Event Timeline");
            cy.findByRole("table")
                .get("#event-history-table")
                .find("tbody")
                .find(".detail")
                .each(($detailCard) => {
                    cy.wrap($detailCard)
                        .invoke("text")
                        .then((text) => {
                            expect(text.length).to.be.greaterThan(1);
                        });
                });
        });

        // File Hosting Activity, Detection Finding Events, Email Activity Events & SMB Activity
        timelineEventsChecks.forEach((eventType) => {
            eventTimelineSingleFilterValidation(eventType[0], eventType[1], eventType[2]);
        });
    });
});
