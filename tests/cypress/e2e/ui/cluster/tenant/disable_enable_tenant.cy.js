/// <reference types="cypress"/>

import { logoutWithinTest, retryLoginWithReload } from "../../iris/account/account_utils";

import { disableTenant, enableTenant, navigateTenantDetails } from "./tenant_utils";

describe("Cluster: Tenants -> Disable & Enable", { tags: ["full", "cluster_test"] }, () => {
    before(() => {
        cy.clearCookies();
        cy.visit(Cypress.env("credentials").CLUSTER_UI);
    });

    const tenant = {
        name: Cypress.env("credentials").CYPRESS_DISABLE.UI_NAME,
        id: Cypress.env("credentials").CYPRESS_DISABLE.UI_ID,
    };

    it("Disable & Enable Tenant & Validate", { tags: "regression" }, () => {
        cy.manualLogin(
            Cypress.env("credentials").CLUSTER_USER_IDS.ADMIN,
            Cypress.env("credentials").CLUSTER_ADMIN_PASSWORD
        );
        navigateTenantDetails(tenant.name);
        disableTenant();
        cy.visit(Cypress.env("credentials").CYPRESS_DISABLE.URL);
        cy.manualLogin(
            Cypress.env("credentials").CYPRESS_DISABLE.ADMIN_USERNAME,
            Cypress.env("credentials").CYPRESS_DISABLE.ADMIN_PASS
        );
        cy.findByText(`Access to ${tenant.name} data is disabled`).should("be.visible");
        cy.visit(Cypress.env("credentials").CLUSTER_UI);
        navigateTenantDetails(tenant.name);
        enableTenant();
        logoutWithinTest();
        cy.visit(Cypress.env("credentials").CYPRESS_DISABLE.URL);
        retryLoginWithReload(tenant.name);
        cy.navigateTopNav("Console", "Overview");
        cy.contains("Overview").should("be.exist");
    });

    after(() => {
        cy.clickLogout();
    });
});
