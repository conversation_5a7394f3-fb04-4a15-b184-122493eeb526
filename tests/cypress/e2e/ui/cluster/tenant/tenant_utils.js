// Methods

export function searchTenantByName(tenantName) {
    cy.setInput("Tenant Details", tenantName);
}

export function navigateTenantDetails(tenantName) {
    searchTenantByName(tenantName);
    cy.clickViewDetails("Actions", `View ${tenantName} details`, "title");

    cy.findByText("Tenant Details").should("be.visible");
}

export function disableTenant() {
    cy.findByRole("button", { name: "Disable" }).should("be.enabled").click();
    cy.findByRole("button", { name: "Yes" }).should("be.visible").click();
    cy.findByRole("alert").should("be.visible");
    cy.contains("Tenant is disabled").should("be.visible");
    cy.findByRole("button", { name: "Enable" }).should("be.visible");
    cy.findByRole("button", { name: "Submit" }).should("be.disabled");
}

export function selectSecurityEntitlements(select = true) {
    if (select) {
        cy.findByLabelText("Security Hygiene").then(($checkbox) => {
            if (!$checkbox.is(":checked")) {
                cy.findByLabelText("Security Hygiene").check();
            }
        });

        cy.findByLabelText("Security Threats").then(($checkbox) => {
            if (!$checkbox.is(":checked")) {
                cy.findByLabelText("Security Threats").check();
            }
        });
    } else if (!select) {
        cy.findByLabelText("Security Hygiene").then(($checkbox) => {
            if ($checkbox.is(":checked")) {
                cy.findByLabelText("Security Hygiene").uncheck();
            }
        });

        cy.findByLabelText("Security Threats").then(($checkbox) => {
            if ($checkbox.is(":checked")) {
                cy.findByLabelText("Security Threats").uncheck();
            }
        });
    }
    cy.findByRole("button", { name: "Submit" }).click();
    cy.contains("Tenant details updated.").should("be.visible");
}

export function enableTenant() {
    cy.findByRole("button", { name: "Enable" }).should("be.enabled").click();
    cy.findByRole("button", { name: "Yes" }).should("be.visible").click();
    cy.findByRole("alert").should("be.visible");
    cy.contains("Tenant is enabled").should("be.visible");
    cy.findByRole("button", { name: "Disable" }).should("be.visible");
    cy.findByRole("button", { name: "Submit" }).should("be.enabled");
}
