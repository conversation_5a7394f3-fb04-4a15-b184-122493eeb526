const assertTermDescription = (termText, modalTitle = null) => {
    cy.findByText(termText)
        .should("exist")
        .then(($dt) => {
            const $dd = $dt.next("dd");
            cy.wrap($dd).invoke("text").should("exist").and("not.be.empty");
            if (modalTitle) {
                cy.wrap($dd).findByTitle(modalTitle).click();
                cy.findByText("Audit Trace Details").should("be.visible");
                cy.get(".modal-dialog.modal-on-top.modal-lg")
                    .should("have.attr", "role", "document")
                    .within(() => {
                        cy.findByText("Close").scrollIntoView({ ensureScrollable: false }).click();
                    });
            }
        });
};

const verifyEntityViewDetailsForDevice = () => {
    cy.clickViewDetails("Hostname", "View Details", "title");
    assertTermDescription("Type", "Open Backtrace Modal");
    assertTermDescription("Risk Level");
    assertTermDescription("Organization");
    assertTermDescription("Groups");
    assertTermDescription("OS Name");
    assertTermDescription("OS Version");
    assertTermDescription("MAC Address", "Open Backtrace Modal");
    assertTermDescription("IP Address", "Open Backtrace Modal");
    assertTermDescription("Owner");
    cy.findByText("Additional Owners")
        .should("exist")
        .then(($dt) => {
            const $dd = $dt.next("dd");
            cy.wrap($dd)
                .invoke("text")
                .then((addOwners) => {
                    if (addOwners !== "-") {
                        cy.wrap($dd)
                            .find("li")
                            .first()
                            .invoke("text")
                            .then((ownerText) => {
                                cy.wrap(ownerText).should("exist").and("not.be.empty");
                                cy.wrap($dd)
                                    .findByText(ownerText, { exact: false })
                                    .should("exist")
                                    .should("be.visible")
                                    .first()
                                    .scrollIntoView({ ensureScrollable: false })
                                    .as("ownerElement");
                                cy.get("@ownerElement").scrollIntoView().click();
                                cy.wrap(ownerText).should("exist").and("not.be.empty");
                                cy.go("back");
                            });
                    }
                });
        });
    assertTermDescription("Environment");
    cy.findByRole("button", { name: "Back" }).click();
    cy.findByText("Search").should("be.visible").and("exist");
};

const verifyEntityViewDetailsForUser = () => {
    cy.clickViewDetails("Email Address", "View Details", "title");
    assertTermDescription("Organization");
    assertTermDescription("Type");
    assertTermDescription("Account");
    assertTermDescription("Name", "Open Backtrace Modal");
    assertTermDescription("Full Name");
    assertTermDescription("Email Address", "Open Backtrace Modal");
    cy.findByText("Manager")
        .should("exist")
        .then(($dt) => {
            const $dd = $dt.next("dd");
            cy.wrap($dd)
                .invoke("text")
                .then((managerName) => {
                    if (managerName !== "-") {
                        cy.findByText(managerName).click();
                        cy.wrap(managerName).should("exist").and("not.be.empty");
                        cy.go("back");
                    }
                });
        });
    assertTermDescription("Groups");
    cy.findByText("Devices Owned")
        .should("exist")
        .then(($dt) => {
            const $dd = $dt.next("dd");
            cy.wrap($dd)
                .invoke("text")
                .then((deviceOwned) => {
                    if (deviceOwned !== "-") {
                        cy.wrap($dd)
                            .find("li")
                            .first()
                            .invoke("text")
                            .then((deviceText) => {
                                cy.wrap(deviceText).should("exist").and("not.be.empty");
                                cy.wrap($dd)
                                    .findByText(deviceText, { exact: false })
                                    .should("exist")
                                    .should("be.visible")
                                    .first()
                                    .scrollIntoView({ ensureScrollable: false })
                                    .as("deviceElement");
                                cy.get("@deviceElement")
                                    .should("be.visible")
                                    .should("have.length", 1)
                                    .should("not.be.disabled")
                                    .click({ force: true });
                                cy.wrap(deviceText).should("exist").and("not.be.empty");
                            });
                    }
                });
        });
};

const verifyEntityHistoryFilter = () => {
    cy.get("#event-history").findByRole("button", { name: "Filter" }).click({ force: true });
    cy.get("#event-history").findByRole("button", { name: "Clear Filters" }).click();
    const additionalEvents = ["Group Management"];

    additionalEvents.forEach((event) => {
        cy.get(".event-history-filters").within(() => {
            cy.findByRole("button", { name: new RegExp(event, "i") })
                .should("exist")
                .scrollIntoView()
                .should("be.enabled")
                .then(($button) => {
                    if ($button.length > 0) {
                        cy.wrap($button).click();
                    }
                });
        });
    });
    cy.get("#event-history").findByRole("button", { name: "Filter" }).should("be.enabled").click();
    cy.findByRole("button", { name: "Clear Filters" }).should("not.exist");

    const scrollAndVerifyEvents = (index = 0) => {
        if (index >= additionalEvents.length) {
            return;
        }

        cy.findByTestId("virtuoso-scroller").then(($scroller) => {
            const isScrollerVisible = $scroller.prop("scrollHeight") > $scroller.prop("clientHeight");

            const verifyEvent = () => {
                cy.findByTestId("virtuoso-item-list")
                    .contains("td", additionalEvents[index])
                    .should("exist")
                    .then(($td) => {
                        if ($td.length > 0) {
                            if (index < additionalEvents.length - 1) {
                                scrollAndVerifyEvents(index + 1);
                            }
                        } else if (isScrollerVisible) {
                            cy.findByTestId("virtuoso-scroller")
                                .scrollTo("bottom")
                                .then(() => {
                                    scrollAndVerifyEvents(index);
                                });
                        }
                    });
            };

            if (isScrollerVisible) {
                cy.findByTestId("virtuoso-scroller")
                    .scrollTo("top")
                    .then(() => {
                        verifyEvent();
                    });
            } else {
                verifyEvent();
            }
        });
    };

    scrollAndVerifyEvents();
};

const verifyEntityLinksColumn = (columnNames) => {
    columnNames.forEach((column) => {
        cy.findByRole("table")
            .find("th")
            .should("exist")
            .filter((_, el) => el.textContent.trim() === column.name)
            .invoke("index")
            .then((columnIndex) => {
                cy.findByRole("table")
                    .find("tbody")
                    .find("tr")
                    .first()
                    .find("td")
                    .eq(columnIndex)
                    .within(($link) => {
                        if ($link.length > 0) {
                            cy.findByTitle(`${column.links}`).should("be.visible");
                        }
                    });
            });
    });
};

const clearAndSelectEventTimelineFilters = (filterNames) => {
    cy.findByText("Filter", { selector: "button.btn.btn-secondary.btn-sm" }).click();
    cy.findByRole("button", { name: "Clear Filters" }).should("be.visible").click();
    cy.findByText(/0 out of \d+ total events/).should("exist");
    filterNames.forEach((filter) => {
        cy.get("button.btn-secondary").contains(filter).click({ force: true });
    });
    cy.findByText("Filter", { selector: "button.btn.btn-secondary.btn-sm" }).click();
    cy.findByRole("button", { name: "Clear Filters" }).should("not.exist");
};

// Recursively processes rows to find and click the "View Details" link
// for rows where the selected owner does not include "-".
const getValidSelectedOwnerAndClickDeviceDetails = () => {
    // Find the index of the column with the specified name
    cy.findByRole("table")
        .find("th")
        .should("exist")
        .filter(":contains('Selected Owner')")
        .invoke("index")
        .then((columnIndex) => {
            // Recursively process each row in the table
            const processRows = (rows, index = 0) => {
                if (index >= rows.length) {
                    return; // Exit recursion if no more rows
                }

                cy.wrap(rows[index])
                    .find("td")
                    .eq(columnIndex)
                    .invoke("text")
                    .then((ownerName) => {
                        if (!ownerName.includes("-")) {
                            // Find and click the "View Details" link if the condition is met
                            cy.wrap(rows[index])
                                .find('a[title="View Details"]')
                                .eq(0)
                                .then(($link) => {
                                    if ($link.length) {
                                        cy.wrap($link).click();
                                    } else {
                                        // Continue with the next row if the link is not found
                                        processRows(rows, index + 1);
                                    }
                                });
                        } else {
                            // Continue with the next row if the condition is not met
                            processRows(rows, index + 1);
                        }
                    });
            };

            // Get all rows from the table body and start processing them
            cy.findByRole("table")
                .find("tbody")
                .find("tr")
                .then((rows) => {
                    processRows(rows.toArray());
                });
        });
};

const eventTimelineSingleFilterValidation = (filterButtonName, eventTypeFilteredParam) => {
    cy.get("#event-history").within(() => {
        cy.findByRole("button", { name: "Filter" }).should("be.enabled").click();
        cy.get(".event-history-filters").within(() => {
            cy.findByRole("button", { name: "Clear Filters" }).should("be.enabled").click();
            cy.findByRole("button", { name: new RegExp(filterButtonName, "i") })
                .should("exist")
                .scrollIntoView()
                .should("be.enabled")
                .click();
        });
        cy.intercept(`**event_types[]=${eventTypeFilteredParam}**`).as("getFilteredActivity");
        cy.findByTitle("Loading...").should("exist");
        cy.findByRole("button", { name: "Filter" }).should("be.enabled").click();
        cy.findByRole("button", { name: "Clear Filters" }).should("not.exist");
        cy.wait("@getFilteredActivity");
        cy.reload();
        cy.findByRole("table").within(() => {
            cy.get("td.detail .text-break.col").each(($el) => {
                const text = $el.text();
                switch (filterButtonName) {
                    case "File Hosting Activity":
                        expect(text.match(/^(Download|Delete)/g)).not.equal(null);
                        break;
                    case "Detection Finding":
                        expect(text.match(/^(Trigger|Triggered)/g)).not.equal(null);
                        break;
                    case "Email Activity":
                        expect(text.startsWith("Email")).to.equal(true);
                        break;
                    case "SMB Activity":
                        expect(text.startsWith("Print")).to.equal(true);
                        break;
                    default:
                        cy.log("Invalid Filter Option");
                        break;
                }
            });
        });
        if (filterButtonName === "Select All Filters") {
            cy.findByRole("button", { name: "Filter" }).sibli;
            cy.get("selector-for-your-element").should(($el) => {
                const text = $el.text().trim();
                const match = text.match(/(\d+)\s+out\s+of\s+(\d+)\s+total\s+events/);

                expect(match).to.not.be.null;
                const firstNumber = parseInt(match[1]);
                const secondNumber = parseInt(match[2]);

                expect(firstNumber).to.equal(secondNumber);
            });
        }
    });
};

const validateEventTextOnly = (fullOrPartialText) => {
    cy.get(".text-break.col").then(($items) => {
        const texts = [...$items].map((item) => item.innerText);
        const hasText = texts.some((text) => text.includes(fullOrPartialText));
        expect(hasText).to.be.equal(true);
    });
};

const findEventValidateEventDataInScrollableEventList = (
    expectedText,
    numberOfEvidence = 0,
    maxScrolls = 1,
    maxWait = 1
) => {
    let scrollCount = 0;

    function validateEventData() {
        cy.get(".text-break.col").then(($elements) => {
            const texts = [...$elements].map((el) => el.innerText.trim());
            if (texts.some((text) => text.includes(expectedText))) {
                expect(true).to.be.equal(true);
                cy.wrap($elements).each(($el) => {
                    cy.wrap($el)
                        .invoke("text")
                        .then((text) => {
                            if (text.trim().includes(expectedText)) {
                                switch (expectedText) {
                                    case "Triggered 'MITRE ATT&CK Techniques Detection Chain'":
                                    case "Triggered 'Proactive Phishing Detection Chain'":
                                        cy.wrap($el.find("button[title='View Detail']")).should("be.enabled");
                                        cy.wrap($el).click();
                                        cy.get(".event-card-item:not(.is-collapsed)").within(() => {
                                            cy.contains("Evidence Artifacts")
                                                .siblings("div")
                                                .within(() => {
                                                    cy.get(".row").should("have.length.at.least", numberOfEvidence);
                                                    if (
                                                        expectedText ===
                                                        "Triggered 'Proactive Phishing Detection Chain'"
                                                    ) {
                                                        cy.get(".row").each(($ele) => {
                                                            cy.wrap($ele)
                                                                .invoke("text")
                                                                .then((txt) => {
                                                                    if (
                                                                        !txt.includes("Email Activity") &&
                                                                        !txt.includes("HTTP Activity")
                                                                    ) {
                                                                        expect(false).to.be.equal(true);
                                                                    }
                                                                });
                                                        });
                                                    }
                                                });
                                        });
                                        cy.wrap($el).click();
                                        cy.get(".event-card-item:not(.is-collapsed)").should("not.exist");
                                        break;
                                    case "NLTM success to desktop-132":
                                        cy.wrap($el.find("button[title='View Detail']")).should("be.enabled");
                                        cy.wrap($el).click();
                                        cy.get(".event-card-item:not(.is-collapsed)").within(($ele) => {
                                            cy.wrap($ele.find(".card-body")).then(() => {
                                                cy.contains("NLTM success to desktop-132").should("be.visible");
                                            });
                                        });
                                        cy.wrap($el).click();
                                        cy.get(".event-card-item:not(.is-collapsed)").should("not.exist");
                                        break;
                                    default:
                                        cy.log("No Evidence Artifacts");
                                }
                            }
                        });
                });
            } else if (scrollCount < maxScrolls) {
                scrollCount += 1;
                cy.findByTestId("virtuoso-scroller").scrollTo("bottom");
                cy.wait(maxWait * 500);
                validateEventData();
            } else {
                throw new Error(`Text "${expectedText}" not found after ${maxScrolls} scrolls`);
            }
        });
    }
    validateEventData();
};

const validateFirstColumnOfUserDetails = (userDetails, nonNumericUserDetails) => {
    cy.get("#event-metadata .col")
        .first()
        .within(() => {
            userDetails.forEach((detail) => {
                const userDetailsValue = cy.contains("dt", detail).next("dd");
                userDetailsValue
                    .should("not.be.empty")
                    .and("not.be.null")
                    .and("not.be.undefined")
                    .and(($dd) => {
                        expect($dd.text().length).to.be.greaterThan(1);
                        if (nonNumericUserDetails.includes(detail)) {
                            const regex = /^[^0-9]*$/;
                            expect($dd.text()).to.match(regex);
                        }
                        if (detail === "Email Address") {
                            const regex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
                            expect($dd.text()).to.match(regex);
                        }
                    });
                if (detail === "Manager") {
                    userDetailsValue.within(() => {
                        cy.get("a")
                            .should("be.visible")
                            .and("not.be.disabled")
                            .and("have.attr", "href")
                            .and("not.be.empty");
                    });
                }
            });
        });
};

const toggleEventsCheckboxesAndValidate = (toggleStatus) => {
    const isCheck = toggleStatus === "check";
    const method = isCheck ? "check" : "uncheck";
    const assertion = isCheck ? "be.checked" : "not.be.checked";

    cy.get("th .checkbox-container>input")[method]();

    cy.findByTestId("virtuoso-item-list").within(() => {
        cy.get("input[type='checkbox']").each(($checkbox) => {
            cy.wrap($checkbox).should(assertion);
        });
    });
};

const toggleOwnedDevicesCheckboxAndValidate = (toggleStatus) => {
    const isCheck = toggleStatus === "check";
    const method = isCheck ? "check" : "uncheck";
    const assertion = isCheck ? "be.checked" : "not.be.checked";

    cy.contains("label", "Include Events from Owned Devices")
        .find('input[type="checkbox"]')
        [method]()
        .should(assertion);
    cy.get(".fas.fa-spinner").should("not.exist");
};

const openNodeGraphFilters = () => {
    cy.get("div[role='dialog']").should("not.exist");
    cy.get("#related-entities").within(() => {
        cy.findByRole("button", { name: "Filter" }).should("be.visible").click();
    });
    cy.get("div[role='dialog']").should("exist");
};

const applyNodeGraphSelectFilter = (selectName, selectOption) => {
    cy.get(`label[for='${selectName}']`).then(($el) => {
        cy.wrap($el).siblings("div").click();
        cy.findByRole("listbox")
            .should("be.visible")
            .findAllByRole("option")
            .each(($option) => {
                cy.wrap($option)
                    .invoke("text")
                    .then((text) => {
                        if (text === selectOption) {
                            cy.wrap($option).click();
                        }
                    });
            });
    });
};

export {
    applyNodeGraphSelectFilter,
    assertTermDescription,
    clearAndSelectEventTimelineFilters,
    eventTimelineSingleFilterValidation,
    findEventValidateEventDataInScrollableEventList,
    getValidSelectedOwnerAndClickDeviceDetails,
    openNodeGraphFilters,
    toggleEventsCheckboxesAndValidate,
    toggleOwnedDevicesCheckboxAndValidate,
    validateEventTextOnly,
    validateFirstColumnOfUserDetails,
    verifyEntityHistoryFilter,
    verifyEntityLinksColumn,
    verifyEntityViewDetailsForDevice,
    verifyEntityViewDetailsForUser,
};
