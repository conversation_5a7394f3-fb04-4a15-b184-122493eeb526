/// <reference types="cypress"/>

describe("Tenant: Configuration -> Security -> Active Detections", { tags: ["config", "full"] }, () => {
    beforeEach(() => {
        cy.loginSession(
            Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
            Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
        );
    });

    after(() => {
        cy.clickLogout();
    });

    const validBucketConfig = {
        "Detect On Statuses *": "test",
        "Detect On Tags *": "DataBee-Detection-Testing",
        "Disable Detect On Tags *": "DataBee-Disable-Testing",
        "S3 Output Bucket": Cypress.env("S3_BUCKET_NAME"),
    };

    const deleteBucketConfig = {
        "Detect On Statuses *": "test",
        "Detect On Tags *": "DataBee-Detection-Testing",
        "Disable Detect On Tags *": "DataBee-Disable-Testing",
    };

    const invalidBucketConfig = {
        "S3 Output Bucket": "test",
    };

    it("Verify that admin can provide valid bucket to perform test connectivity", { tags: ["smoke", "config"] }, () => {
        cy.navigateTopRightNav("Configuration", "Security");
        cy.findByRole("link", { name: "Active Detections" }).should("be.visible");
        cy.contains("h2", "Active Detections").should("be.visible");
        Object.values(deleteBucketConfig).forEach((value) => {
            cy.deleteValueFromMultiSelectField(value);
        });
        cy.task("createBucket").then(() => {
            cy.updateForm(validBucketConfig);
        });
        cy.findByText("Apply this IAM policy to your configured S3 bucket to allow DataBee to write detections").should(
            "be.visible"
        );
        cy.findByRole("button", { name: "Copy Content" }) // Select the button element
            .closest(".copyable-text-blob") // go to the wrapper div
            .find("pre") // find the <pre> tag inside it
            .invoke("text") // Get the text content of the div
            .then((policyParams) => {
                cy.task("putS3BucketPolicy", policyParams).then(() => {
                    cy.findByRole("button", { name: "Test Connectivity" }).click();
                });
            });
        cy.findByText(`S3 bucket ${Cypress.env("S3_BUCKET_NAME")} is accessible.`).should("be.visible");
        // remove bucket policy
        cy.task("deleteS3BucketPolicy");
    });

    it(
        "Verify that admin can provide invalid bucket to perform test connectivity",
        { tags: ["regression", "config"] },
        () => {
            cy.navigateTopRightNav("Configuration", "Security");
            cy.findByRole("link", { name: "Active Detections" }).should("be.visible");
            cy.contains("h2", "Active Detections").should("be.visible");
            cy.updateForm(invalidBucketConfig);
            cy.findByText(
                "Apply this IAM policy to your configured S3 bucket to allow DataBee to write detections"
            ).should("be.visible");
            cy.findByRole("button", { name: "Test Connectivity" }).click();
            cy.findByText(
                `Permission denied to connect to S3 bucket ${
                    Object.values(invalidBucketConfig)[0]
                }, check its configuration`
            ).should("be.visible");
            cy.findByLabelText("close").click();
        }
    );

    it(
        "Verify that admin without providing the bucket to perform test connectivity",
        { tags: ["regression", "config"] },
        () => {
            cy.navigateTopRightNav("Configuration", "Security");
            cy.findByRole("link", { name: "Active Detections" }).should("be.visible");
            cy.contains("h2", "Active Detections").should("be.visible");
            cy.findByLabelText("S3 Output Bucket")
                .parent()
                .within(($el) => {
                    cy.wrap($el).find("input").clear();
                });
            cy.findByRole("button", { name: "Test Connectivity" }).click();
            cy.findByText("S3 bucket name is required.").should("be.visible");
            cy.findByLabelText("close").click();
        }
    );

    it("Verify that admin can submit the configuration", { tags: ["regression", "config"] }, () => {
        cy.navigateTopRightNav("Configuration", "Security");
        cy.findByRole("link", { name: "Active Detections" }).should("be.visible");
        cy.contains("h2", "Active Detections").should("be.visible");
        Object.values(deleteBucketConfig).forEach((value) => {
            cy.deleteValueFromMultiSelectField(value);
        });
        cy.task("createBucket").then(() => {
            cy.updateForm(validBucketConfig);
        });
        cy.findByText("Apply this IAM policy to your configured S3 bucket to allow DataBee to write detections").should(
            "be.visible"
        );
        cy.findByRole("button", { name: "Copy Content" }) // Select the button element
            .closest(".copyable-text-blob") // go to the wrapper div
            .find("pre") // find the <pre> tag inside it
            .invoke("text") // Get the text content of the div
            .then((policyParams) => {
                cy.task("putS3BucketPolicy", policyParams).then(() => {
                    cy.findByRole("button", { name: "Submit" }).click();
                });
            });
        cy.contains("Configuration updated successfully.").should("be.visible");
        // remove bucket policy
        cy.task("deleteS3BucketPolicy");
    });

    it(
        "Verify that admin can provide invalid bucket and submit the configuration",
        { tags: ["regression", "config"] },
        () => {
            cy.navigateTopRightNav("Configuration", "Security");
            cy.findByRole("link", { name: "Active Detections" }).should("be.visible");
            cy.contains("h2", "Active Detections").should("be.visible");
            cy.updateForm(invalidBucketConfig);
            cy.findByRole("button", { name: "Submit" }).click();
            cy.findByText(
                `Permission denied to connect to S3 bucket ${
                    Object.values(invalidBucketConfig)[0]
                }, check its configuration`
            ).should("be.visible");
            cy.findByLabelText("close").click();
        }
    );

    it("Verify that admin can not enter duplicate values", { tags: ["regression", "config"] }, () => {
        const randomString = Math.random().toString(36).substring(8);
        const newTagName = `Detect-On-Statuses-${randomString}`;

        cy.navigateTopRightNav("Configuration", "Security");
        cy.findByRole("link", { name: "Active Detections" }).should("be.visible");
        cy.contains("h2", "Active Detections").should("be.visible");

        cy.findByLabelText("Detect On Statuses *").type(`${newTagName}{enter}`);
        cy.findByText(newTagName).should("have.length", 1);
        // entering the same tag value again to check duplication does not occur
        cy.findByLabelText("Detect On Statuses *").type(`${newTagName}{enter}`);
        cy.findByText(newTagName).should("have.length", 1);
    });

    it(
        "Verify that admin provides multiple items and submit the configuration",
        { tags: ["regression", "config"] },
        () => {
            cy.navigateTopRightNav("Configuration", "Security");
            cy.findByRole("link", { name: "Active Detections" }).should("be.visible");
            cy.contains("h2", "Active Detections").should("be.visible");
            Object.values(deleteBucketConfig).forEach((value) => {
                cy.deleteValueFromMultiSelectField(value);
            });
            const multiValueBucketConfig = {
                "Detect On Statuses *": "stable",
                "Detect On Tags *": "DataBee-Detection-Testing-2",
                "Disable Detect On Tags *": "DataBee-Disable-Testing-2",
            };

            // Adding multiple tags in form
            cy.task("createBucket").then(() => {
                cy.updateForm(validBucketConfig);
            });
            Object.values(multiValueBucketConfig).forEach((value) => {
                cy.deleteValueFromMultiSelectField(value);
            });
            cy.updateForm(multiValueBucketConfig);

            cy.findByText(
                "Apply this IAM policy to your configured S3 bucket to allow DataBee to write detections"
            ).should("be.visible");
            cy.findByRole("button", { name: "Copy Content" }) // Select the button element
                .closest(".copyable-text-blob") // go to the wrapper div
                .find("pre") // find the <pre> tag inside it
                .invoke("text") // Get the text content of the div
                .then((policyParams) => {
                    cy.task("putS3BucketPolicy", policyParams).then(() => {
                        cy.findByRole("button", { name: "Submit" }).click();
                    });
                });
            cy.contains("Configuration updated successfully.").should("be.visible");
            // remove bucket policy
            cy.task("deleteS3BucketPolicy");
        }
    );

    it("Verify that admin can not submit with empty tag list", { tags: ["regression", "config"] }, () => {
        cy.navigateTopRightNav("Configuration", "Security");
        cy.findByRole("link", { name: "Active Detections" }).should("be.visible");
        cy.contains("h2", "Active Detections").should("be.visible");
        Object.values(deleteBucketConfig).forEach((value) => {
            cy.deleteValueFromMultiSelectField(value);
        });

        const labelTexts = ["Detect On Statuses *", "Detect On Tags *", "Disable Detect On Tags *"];

        // verify that submit button is disabled if tag list is empty
        cy.updateForm(validBucketConfig);
        labelTexts.forEach((labelText) => {
            cy.findByText(labelText, { selector: "label" }).parent().find(".react-select__clear-indicator").click();
            cy.findByRole("button", { name: "Submit" }).should("be.disabled");
            // clear one field at a time and test submit button disabled
            cy.findByLabelText(labelText).type("test");
            cy.findByLabelText(labelText).type("{enter}");
        });
    });
});
