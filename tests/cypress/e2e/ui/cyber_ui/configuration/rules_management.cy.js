import { deleteAllRulesConfig } from "../../shared/configuration/config_utils";

describe("Tenant: Configuration -> Security -> Rules Management", { tags: ["config", "full"] }, () => {
    const createdRepos = [];
    beforeEach(() => {
        cy.session(
            [Cypress.env("credentials").DATABEE_USER_IDS.ADMIN, Cypress.env("credentials").DATABEE_ADMIN_PASSWORD],
            () => {
                cy.visit(Cypress.env("credentials").DATABEE_UI);
                cy.manualLogin(
                    Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
                    Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
                );
            }
        );
        cy.visit(`${Cypress.env("credentials").DATABEE_UI}console/overview`);
    });

    after(() => {
        cy.clickLogout();
        deleteAllRulesConfig();
    });
    const randomString = Math.random().toString(36).substring(8);
    const itemName = `SigmaHQ-${randomString}`;
    const invalidRulesConfig = {
        "Item Name *": itemName,
        "URL *": "https://github.com/TestHQ/test.git",
        "Branch *": "master",
    };

    const validRulesConfig = {
        "Item Name *": itemName,
        "URL *": Cypress.env("credentials").GITHUB_URL,
        "Branch *": "master",
    };

    it(
        "Verify that a form can not be submitted when invalid github repo is entered in the field.",
        { tags: ["regression", "config"] },
        () => {
            cy.navigateTopRightNav("Configuration", "Security");
            cy.findByText("Rules Management").click();
            cy.findByRole("button", { name: "Add Github Repository" }).click();
            cy.findAllByRole("button", { name: /submit/i }).should("be.disabled");
            cy.findByRole("button", { name: "Test Connectivity" }).click();
            cy.findByText("Repo URL is required.").should("be.visible");
            cy.findByLabelText("close").click();
            cy.updateForm(invalidRulesConfig);
            cy.findByRole("button", { name: "Test Connectivity" }).click();
            cy.findByText(`Repository not found: ${Object.values(invalidRulesConfig)[1]}`).should("be.visible");
            cy.findByLabelText("close").click();
            cy.findAllByRole("button", { name: /submit/i })
                .filter(":not([disabled])")
                .click();
            cy.findByText(`Repository not found: ${Object.values(invalidRulesConfig)[1]}`).should("be.visible");
            cy.findByLabelText("close").click();
            cy.findByLabelText("Close").click();
        }
    );

    it("Verify that admin can not submit with empty mandatory list", { tags: ["regression", "config"] }, () => {
        cy.navigateTopRightNav("Configuration", "Security");
        cy.findByText("Rules Management").click();
        cy.findByRole("button", { name: "Add Github Repository" }).click();
        cy.findAllByText("Add Github Repository").should("be.visible");
        const labelTexts = ["Item Name *", "URL *", "Branch *"];

        // verify that submit button is disabled if mandatory field is empty
        cy.updateForm(validRulesConfig);
        labelTexts.forEach((labelText) => {
            cy.findByText(labelText, { selector: "label" })
                .invoke("attr", "for")
                .then((id) => {
                    cy.get(`#${id}`).clear();
                });
            cy.findAllByText("Required!").should("be.visible");
            cy.findAllByRole("button", { name: /submit/i }).should("be.disabled");
        });
        cy.findByLabelText("Close").click();
    });

    it("Verify that admin can disable and enable the configuration", { tags: ["regression", "config"] }, () => {
        cy.navigateTopRightNav("Configuration", "Security");
        cy.findByText("Rules Management").click();
        cy.findByRole("button", { name: "Add Github Repository" }).click();
        cy.findAllByText("Add Github Repository").should("be.visible");
        cy.updateForm(validRulesConfig);
        createdRepos.push(Object.values(validRulesConfig)[0]);
        cy.findAllByRole("button", { name: /submit/i })
            .filter(":not([disabled])")
            .click();
        cy.contains("p", `${Object.values(validRulesConfig)[0]}`).click();
        cy.findByRole("button", { name: "Disable" }).click();
        cy.contains("h5", `Disable ${Object.values(validRulesConfig)[0]}`).should("be.visible");
        cy.findAllByRole("dialog")
            .last()
            .findByRole("group")
            .within(() => {
                cy.findByText("Close").should("be.enabled");
                cy.findByText("Disable").click();
            });
        cy.findByText("Configuration updated successfully.").should("be.visible");
        cy.contains("p", `${Object.values(validRulesConfig)[0]}`).click();
        cy.findByRole("alert")
            .findByText(
                `${Object.values(validRulesConfig)[0]} is disabled. To re-enable click on the enable button below.`
            )
            .should("be.visible");
        cy.findByRole("button", { name: "Delete" }).should("be.enabled");
        cy.findByRole("button", { name: "Test Connectivity" }).should("be.enabled");
        cy.findAllByRole("button", { name: /submit/i }).should("be.disabled");
        cy.findByRole("button", { name: "Enable" }).click();
        cy.contains("h5", `Enable ${Object.values(validRulesConfig)[0]}`).should("be.visible");
        cy.findAllByRole("dialog")
            .last()
            .findByRole("group")
            .within(() => {
                cy.findByText("Close").should("be.enabled");
                cy.findByText("Enable").click();
            });
        cy.findByText("Configuration updated successfully.").should("be.visible");
    });

    it("Admin User can update fetch frequency and can perform update now", { tags: ["regression", "config"] }, () => {
        cy.navigateTopRightNav("Configuration", "Security");
        cy.findByText("Rules Management").click();

        // Admin User can update fetch frequency
        const randomNumber = Math.floor(Math.random() * 100);
        cy.setInput("Fetch Frequency *", randomNumber);
        cy.findByRole("button", { name: "Submit" }).should("be.enabled").click();
        cy.validateToastMessage("Configuration updated successfully.");
        cy.get('input[type="number"]').should("have.value", randomNumber);

        // Admin User can perform update now
        cy.findByRole("button", { name: "Update Now" }).should("be.enabled").click();
        cy.get(".modal-content").within(() => {
            cy.findByText("This will force a pull event for all configured repositories, proceed?").should(
                "be.visible"
            );
            cy.findByRole("button", { name: "Update Now" }).should("be.enabled").click();
        });
        cy.validateToastMessage("Updating all configured repositories");
        cy.findByLabelText("Close").click();
    });

    it("Verify that admin can submit the configuration", { tags: ["smoke", "config"] }, () => {
        cy.navigateTopRightNav("Configuration", "Security");
        cy.findByText("Rules Management").click();
        cy.findByRole("button", { name: "Add Github Repository" }).click();
        cy.findAllByText("Add Github Repository").should("be.visible");
        cy.updateForm(validRulesConfig);
        createdRepos.push(Object.values(validRulesConfig)[0]);
        cy.findAllByRole("button", { name: /submit/i })
            .filter(":not([disabled])")
            .click();
        cy.findByText("Configuration created successfully").should("be.exist");
        cy.contains("p", `${Object.values(validRulesConfig)[0]}`).should("be.visible");
    });

    it("Verify that admin can edit the configuration", { tags: ["regression", "config"] }, () => {
        cy.navigateTopRightNav("Configuration", "Security");
        cy.findByText("Rules Management").click();
        cy.findByRole("button", { name: "Add Github Repository" }).click();
        cy.findAllByText("Add Github Repository").should("be.visible");
        cy.updateForm(validRulesConfig);
        createdRepos.push(Object.values(validRulesConfig)[0]);
        cy.findAllByRole("button", { name: /submit/i })
            .filter(":not([disabled])")
            .click();
        cy.contains("p", `${Object.values(validRulesConfig)[0]}`).click();
        cy.findByRole("button", { name: "Disable" }).should("be.enabled");
        cy.findByRole("button", { name: "Delete" }).should("be.enabled");
        cy.findByRole("button", { name: "Test Connectivity" }).should("be.enabled");
        cy.findAllByRole("button", { name: /submit/i }).should("be.disabled");
        const labelTexts = ["Item Name *", "URL *", "Branch *"];
        labelTexts.forEach((labelText) => {
            cy.findByText(labelText, { selector: "label" })
                .invoke("attr", "for")
                .then((id) => {
                    cy.get(`#${id}`).clear();
                });
            cy.findAllByText("Required!").should("be.visible");
            cy.findAllByRole("button", { name: /submit/i }).should("be.disabled");
        });
        cy.updateForm(validRulesConfig);

        cy.findAllByRole("button", { name: /submit/i })
            .filter(":not([disabled])")
            .click();
        cy.findByText("Configuration updated successfully.").should("be.visible");
    });
});
