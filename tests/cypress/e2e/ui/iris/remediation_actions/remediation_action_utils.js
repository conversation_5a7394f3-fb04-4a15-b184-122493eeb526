// eslint-disable-next-line import/no-unresolved -- temp
import { CronExpressionParser } from "cron-parser";

import { addParameterIfNeeded } from "../feeds/feeds_utils";
import { setSearchParams } from "../search/search_utils";

const navigateToRemediationActions = () => {
    cy.navigateTopNav("Data", "Remediation Actions");
    cy.findByRole("heading", { level: 2 })
        .invoke("text")
        .then((text) => {
            expect(text).to.equal("Actions");
        });
    cy.retryTextNotContainCheck(".alert.alert-info.fade.show", " Loading");
    cy.contains("Searching...").should("not.exist");
    cy.checkForErrors("Remediation Actions", []);
    cy.url().should("include", "remediation-actions");
};

const actionSearch = (searchTerm) => {
    cy.findByRole("button", { name: "Clear" }).should("be.visible").and("be.disabled");
    cy.get("#search-query-text").type(searchTerm);
    cy.get("#search-query-text").should("have.value", searchTerm);
    cy.findByRole("button", { name: "Clear" }).should("be.visible").and("be.enabled");
};

const searchAndSelectAction = (searchTerm) => {
    cy.contains("Searching...").should("not.exist");
    actionSearch(searchTerm);
    cy.contains("Searching...").should("not.exist");
    cy.get("#actions-table tbody tr").then(($rows) => {
        if ($rows.length > 0) {
            cy.wrap($rows.first().find("td").eq(1).find("button")).should("contain.text", searchTerm);
            cy.wrap($rows.first().find("td").first().find('input[type="checkbox"]')).check().should("be.checked");
        }
    });
};

const clearSearchedAction = () => {
    cy.findByRole("button", { name: "Clear" }).should("be.visible").and("be.enabled").click();
    cy.get("#search-query-text").should("have.value", "");
};

const openCreateNewActionDialog = () => {
    cy.findByRole("button", { name: "New" }).should("be.enabled").click();
    cy.findByRole("dialog").should("be.visible");
    cy.findByText("Create New Action").should("be.visible");
};

const validateStep1FormFields = () => {
    const step1Fields = [/Connection Name/i, /Action Name/i, /Description/i, /Schema/i, /Table/i];

    step1Fields.forEach((field) => {
        cy.findByLabelText(field).should("be.visible");
    });
};

const configureServiceNowIntegration = (
    authMethod,
    username,
    password,
    clientKey,
    clientSecret,
    tokenUrl,
    testConnection = true
) => {
    cy.findByLabelText("Authorization Method *").focus().type(authMethod).type("{enter}", { force: true });
    cy.findByLabelText("Username *").clear().type(username);
    cy.findByLabelText("Password *").clear().type(password);
    cy.findByLabelText("Client Key *").clear().type(clientKey);
    cy.findByLabelText("Client Secret *").clear().type(clientSecret);
    cy.findByLabelText("Token URL *").clear().type(tokenUrl);
    cy.intercept("POST", "/api/remediation-actions/connections/servicenow/test/").as("saveIntegration");
    if (testConnection) {
        cy.findByRole("button", { name: "Test Connection" }).click();
        cy.log("Testing the ServiceNow Integration Connection!");
        cy.validateToastMessage("Connection successful");
    }
    cy.findByRole("button", { name: "Save" }).click();
    cy.log("Saving the ServiceNow Integration!");
    cy.wait("@saveIntegration").its("response.statusCode").should("eq", 200);
    cy.validateToastMessage("Configuration updated successfully.");
};

const validateServiceNowIntegrationFields = (authMethod, username, clientKey, tokenUrl) => {
    const MASKED = "****";
    cy.findByLabelText("Authorization Method *")
        .parents('[class*="react-select"]')
        .find(".react-select__single-value")
        .should("have.text", authMethod);
    cy.findByLabelText("Username *").should("have.value", username);
    cy.findByLabelText("Password *").should("have.value", MASKED);
    cy.findByLabelText("Client Key *").should("have.value", clientKey);
    cy.findByLabelText("Client Secret *").should("have.value", MASKED);
    cy.findByLabelText("Token URL *").should("have.value", tokenUrl);
};

const waitForActionToAppearInTable = (actionName, maxTries = 5, waitBetweenTries = 2000) => {
    let attempts = 0;

    const checkForAction = () => {
        attempts += 1;
        cy.log(`Attempt ${attempts}/${maxTries}: Looking for action "${actionName}"`);

        cy.get("#actions-table tbody tr").then(($rows) => {
            actionSearch(actionName);
            const actionFound = $rows.each((index, row) => {
                const nameText = Cypress.$(row).find("td").eq(1).find("button").text().trim();
                return nameText === actionName;
            });

            if (actionFound) {
                cy.log(`Action "${actionName}" found in table after ${attempts} attempts`);
                cy.get("#actions-table tbody tr").contains("button", actionName).should("be.visible");
                clearSearchedAction();
            } else if (attempts < maxTries) {
                cy.log(`Action "${actionName}" not found. Reloading page... (${attempts}/${maxTries})`);
                cy.reload();
                // eslint-disable-next-line cypress/no-unnecessary-waiting -- This is needed for retry logic
                cy.wait(waitBetweenTries);
                checkForAction();
            } else {
                clearSearchedAction();
                throw new Error(`Action "${actionName}" did not appear in table after ${maxTries} reload attempts`);
            }
        });
    };

    checkForAction();
};

const waitForActionStatusToBeActive = (actionName, maxTries = 15, waitBetweenTries = 10000) => {
    let attempts = 0;

    const checkForActiveStatus = () => {
        attempts += 1;
        cy.log(`Attempt ${attempts}/${maxTries}: Checking if action "${actionName}" status is Active`);

        cy.get("#actions-table tbody tr").then(($rows) => {
            actionSearch(actionName);
            let statusIsActive = false;

            $rows.each((index, row) => {
                const nameText = Cypress.$(row).find("td").eq(1).find("button").text().trim();
                if (nameText === actionName) {
                    const statusText = Cypress.$(row).find("td").eq(3).text().trim();
                    statusIsActive = statusText === "Active";
                }
            });

            if (statusIsActive) {
                cy.log(`Action "${actionName}" status is Active after ${attempts} attempts`);
                clearSearchedAction();
            } else if (attempts < maxTries) {
                cy.log(`Action "${actionName}" status not Active yet. Reloading page... (${attempts}/${maxTries})`);
                // eslint-disable-next-line cypress/no-unnecessary-waiting -- This is needed for retry logic
                cy.wait(waitBetweenTries);
                clearSearchedAction();
                cy.reload();
                checkForActiveStatus();
            } else {
                clearSearchedAction();
                throw new Error(
                    `Action "${actionName}" status did not become Active after ${maxTries} reload attempts`
                );
            }
        });
    };

    checkForActiveStatus();
};

const waitForActionToDisappearInTable = (actionName, maxTries = 5, waitBetweenTries = 2000) => {
    let attempts = 0;

    const checkForActionDisappearance = () => {
        attempts += 1;
        cy.log(`Attempt ${attempts}/${maxTries}: Checking if action "${actionName}" has disappeared`);
        cy.contains("Searching...").should("not.exist");
        cy.get("#actions-table tbody tr").then(($rows) => {
            actionSearch(actionName);
            let actionFound = false;

            $rows.each((index, row) => {
                const nameText = Cypress.$(row).find("td").eq(1).find("button").text().trim();
                actionFound = nameText === actionName;
                if (actionFound) {
                    return false; // Break out of the loop if action is found
                }
                return true; // Continue the loop
            });

            if (!actionFound) {
                cy.log(`Action "${actionName}" has disappeared from table after ${attempts} attempts`);
                clearSearchedAction();
                cy.get("#actions-table tbody tr").should("not.contain", actionName);
            } else if (attempts < maxTries) {
                cy.log(`Action "${actionName}" still exists. Reloading page... (${attempts}/${maxTries})`);
                clearSearchedAction();
                cy.reload();
                // eslint-disable-next-line cypress/no-unnecessary-waiting -- This is needed for retry logic
                cy.wait(waitBetweenTries);
                checkForActionDisappearance();
            } else {
                clearSearchedAction();
                throw new Error(
                    `Action "${actionName}" did not disappear from table after ${maxTries} reload attempts`
                );
            }
        });
    };

    checkForActionDisappearance();
};

/**
 * Wait for "No data lake connection established yet" message to appear
 * @param {number} maxTries - Maximum number of reload attempts (default: 10)
 * @param {number} waitBetweenTries - Wait time between tries in milliseconds (default: 2000)
 */
const waitForNoDataLakeMessage = (maxTries = 10, waitBetweenTries = 2000) => {
    let attempts = 0;

    const checkForMessage = () => {
        attempts += 1;
        cy.log(`Attempt ${attempts}/${maxTries}: Looking for "No datalake connection established yet" message`);
        cy.findByRole("heading", { level: 2 })
            .invoke("text")
            .then((text) => {
                expect(text).to.equal("Actions");
            });
        cy.get("body").then(($body) => {
            const messageExists = $body.text().includes("No datalake connection established yet");

            if (messageExists) {
                cy.log(`"No datalake connection established yet" message found after ${attempts} attempts`);
                cy.contains("No datalake connection established yet").should("be.visible");
            } else if (attempts < maxTries) {
                cy.log(`Message not found. Reloading page... (${attempts}/${maxTries})`);
                cy.reload();
                // eslint-disable-next-line cypress/no-unnecessary-waiting -- This is needed for retry logic
                cy.wait(waitBetweenTries);
                checkForMessage();
            } else {
                throw new Error(
                    `"No datalake connection established yet" message did not appear after ${maxTries} reload attempts`
                );
            }
        });
    };

    checkForMessage();
};

function createNewAction(
    connectionName,
    actionName,
    description,
    schema,
    table,
    query,
    ticketTitle,
    ticketDescription,
    additionalFields,
    schedule,
    daySelection,
    save = true
) {
    validateStep1FormFields();
    // Fill connection and action details
    cy.findByLabelText(/Connection Name/i)
        .click()
        .type(connectionName)
        .type("{enter}", { force: true });
    cy.findByLabelText(/Action Name/i).type(actionName);
    cy.findByLabelText(/Description/i).type(description);
    cy.findByLabelText(/Schema/i)
        .click()
        .type(schema)
        .type("{enter}", { force: true });
    cy.findByLabelText(/Table/i).click().type(table).type("{enter}", { force: true });

    // Ensure all filters are selected
    Object.keys(query).forEach((label) => {
        addParameterIfNeeded(label);
    });
    setSearchParams(query);

    // Navigate to the next step and fill in ticket details
    // Click the "Next" button inside the modal body only
    cy.get(".modal-body").within(() => {
        cy.findByRole("button", { name: "Next" }).should("be.visible").should("not.be.disabled").click({ force: true });
    });
    cy.findByLabelText(/Ticket Title/i)
        .clear()
        .type(ticketTitle);
    cy.findByLabelText(/Ticket Description/i)
        .clear()
        .type(ticketDescription);

    // Handle additional fields
    cy.contains("label", "Add Fields")
        .closest("div")
        .parent()
        .siblings("input")
        .first()
        .clear()
        .type(additionalFields, { parseSpecialCharSequences: false });

    // set schedule
    cy.contains("label", "Schedule").parent().find(".react-select__control").click();
    cy.get(".react-select__option").contains(schedule).click();

    // Handle day selection
    if (typeof daySelection === "string" && daySelection === "All") {
        cy.get('input[name="daySelection"]').check({ force: true });
    } else if (Array.isArray(daySelection)) {
        cy.get('input[name="daySelection"]').click({ force: true });
        cy.get('input[name="daySelection"]').should("not.be.checked");
        daySelection.forEach((day) => {
            cy.contains("button", day).click();
        });
    }

    if (save) {
        // save and validate
        cy.findByRole("button", { name: "Save" }).click();
        cy.validateToastMessage("Action created successfully");
        waitForActionToAppearInTable(actionName);
    } else if (!save) {
        cy.get("body").type("{esc}");
    }
}

const deleteActionByName = (actionName) => {
    actionSearch(actionName);
    cy.get("#actions-table tbody tr").then(($rows) => {
        if ($rows.length > 0) {
            cy.wrap($rows.first().find("td").eq(1).find("button")).should("contain.text", actionName);
            cy.wrap($rows.first().find("td").first().find('input[type="checkbox"]')).check().should("be.checked");
            cy.findByRole("button", { name: "Delete" }).should("be.enabled").click();
            cy.validateToastMessage("Actions deleted successfully");
            clearSearchedAction();
            waitForActionToDisappearInTable(actionName);
        }
    });
};

function getColumnIndexByName(columnName) {
    return cy.get("#actions-table thead th").then(($ths) => {
        const index = [...$ths].findIndex((th) => th.innerText.trim() === columnName);
        if (index === -1) {
            throw new Error(`Column "${columnName}" not found`);
        }
        return index;
    });
}

function getFormattedNYTimePlusMinutes(minutesToAdd = 0) {
    const date = new Date(Date.now() + minutesToAdd * 60000); // Add minutes

    // Format using New York timezone and options
    const formatter = new Intl.DateTimeFormat("en-US", {
        timeZone: "America/New_York",
        month: "long",
        day: "numeric",
        year: "numeric",
        hour: "numeric",
        minute: "2-digit",
        hour12: true,
    });

    const parts = formatter.formatToParts(date);
    const partMap = Object.fromEntries(parts.map((p) => [p.type, p.value]));

    // Add ordinal suffix to the day
    const day = parseInt(partMap.day, 10);
    const ordinalSuffix = (d) => {
        if (d > 3 && d < 21) {
            return "th";
        }
        switch (d % 10) {
            case 1:
                return "st";
            case 2:
                return "nd";
            case 3:
                return "rd";
            default:
                return "th";
        }
    };

    const formatted = `${partMap.month} ${day}${ordinalSuffix(day)} ${partMap.year} ${partMap.hour}:${
        partMap.minute
    } ${partMap.dayPeriod.toLowerCase()}`;
    return formatted;
}

function waitForActionToCreate(actionName, options = {}) {
    const retries = options.retries ?? 50;
    if (retries === 0) {
        return cy.wrap(null).then(() => {
            throw new Error(`"${actionName}" not found after maximum retries`);
        });
    }

    cy.intercept("GET", "/api/remediation-actions/connections/actions/**").as(`getActions.${retries}`);
    cy.contains("h2", "Actions").should("be.visible");
    cy.contains("Searching...").should("not.exist");

    // extract trailing digits or fallback to full actionName
    const searchTerm = actionName.match(/\d+$/)?.[0] || actionName;

    cy.findByPlaceholderText("Search by action name").click({ force: true }).clear().type(searchTerm);

    cy.contains("Searching...").should("not.exist");

    return cy.get("body").then(($body) => {
        if ($body.text().includes(actionName)) {
            cy.log(`Found action: "${actionName}"`);
            return;
        }
        cy.log(`"${actionName}" not found. Reloading... (Retries left: ${retries - 1})`);
        // Navigate to history and back to force reload
        cy.findByRole("button", { name: /history/i }).click();
        cy.contains("h2", "Action History").should("be.visible");
        cy.contains("Searching...").should("not.exist");
        cy.findByRole("button", { name: /actions/i }).click();
        cy.wait(`@getActions.${retries}`);
        waitForActionToCreate(actionName, { retries: retries - 1 });
    });
}

function waitForActionToComplete(actionName, status, columnIndex = 4, options = {}) {
    const retries = options.retries ?? 100;
    if (retries === 0) {
        return cy.wrap(null).then(() => {
            throw new Error(`"${actionName}" with status "${status}" not found after maximum retries`);
        });
    }

    cy.intercept("GET", "/api/remediation-actions/connections/actions/**").as(`getActions.${retries}`);
    cy.contains("h2", "Actions").should("be.visible");
    cy.contains("Searching...").should("not.exist");

    let found = false;
    return cy
        .get("#actions-table tbody tr")
        .each(($row) => {
            const $cells = Cypress.$($row).find("td");
            if ($cells.eq(1).text().includes(actionName)) {
                if ($cells.eq(columnIndex).text().includes(status)) {
                    found = true;
                    cy.log(`Found "${actionName}" with status "${status}"`);
                }
            }
        })
        .then(() => {
            if (found) {
                return;
            }
            cy.log(`"${actionName}" not in status "${status}". Reloading... (Retries left: ${retries - 1})`);
            cy.findByRole("button", { name: /history/i }).click();
            cy.contains("h2", "Action History").should("be.visible");
            cy.contains("Searching...").should("not.exist");
            cy.findByRole("button", { name: /actions/i }).click();
            cy.wait(`@getActions.${retries}`);
            waitForActionToComplete(actionName, status, columnIndex, { retries: retries - 1 });
        });
}

const getDatalakeSchemas = () => {
    cy.intercept("GET", "/api/remediation-actions/connections/actions/**").as("getActions");
    navigateToRemediationActions();
    cy.contains("Searching...").should("not.exist");
    return cy.wait("@getActions").then((interception) => {
        expect(interception.response.statusCode).to.eq(200);
        const { fields } = interception.response.body.new_action_schema.steps[0];
        const querySchemaField = fields.find((field) => field.name === "query_schema");
        return querySchemaField && Array.isArray(querySchemaField.enum)
            ? querySchemaField.enum.map((option) => option.value)
            : [];
    });
};

const validateSchemasAndTablesInForm = (schemas) => {
    navigateToRemediationActions();
    // For each schema, verify that tables present in datalake shown in UI dropdown
    schemas.forEach((schema) => {
        cy.findByRole("button", { name: "New" }).click({ force: true });
        cy.intercept("GET", `/api/remediation-actions/connections/actions/views/${schema}/`).as(`get${schema}Views`);
        cy.selectOption('input[id="config-input-query_schema"]', schema);

        cy.wait(`@get${schema}Views`).then((interception) => {
            expect(interception.response.statusCode).to.eq(200);
            const response = interception.response.body;
            const apiLabels = response.view.map((v) => v.label);
            cy.get(".react-select__control").eq(2).click();
            cy.get(".react-select__option").then(($options) => {
                const optionTexts = [...$options].map((opt) => opt.innerText.trim());
                optionTexts.forEach((optText) => {
                    expect(apiLabels).to.include(optText);
                });
                expect(optionTexts.length).to.eq(apiLabels.length);
            });
        });
        cy.get("body").type("{esc}");
    });
};

const updateSnowflakeDatabase = (databaseName) => {
    cy.navigateTopRightNav("Configuration", "System");
    cy.findByAltText("Snowflake").click();
    cy.findAllByLabelText("Database *").click().clear().type(databaseName);
    cy.findByRole("button", { name: "Submit" }).click();
    cy.validateToastMessage("Configuration updated successfully.");
};

const getActionAPIResponse = (actionID) => {
    cy.findByTitle("My Profile").click();
    cy.findByText("My Profile").click();
    cy.get("#timezone-form-group").find(".react-select__single-value").invoke("text").as("timezone");

    cy.get("body").then(($body) => {
        if ($body.find('button:contains("Generate API Key")').length > 0) {
            cy.findByRole("button", { name: "Generate API Key" }).click();
        } else {
            cy.findByRole("button", { name: "Show API Key" }).click();
        }
    });

    cy.findByRole("button", { name: "Revoke API Key" }).should("be.visible").click();
    cy.validateToastMessage("API token successfully revoked");
    cy.findByRole("button", { name: "Generate API Key" }).should("be.visible").click();
    cy.findByRole("button", { name: "Hide API Key" }).should("be.visible");
    cy.findAllByText("Copy to clipboard").should("be.visible");

    return cy
        .findByLabelText("API Key")
        .invoke("val")
        .then((APIKey) => {
            cy.request({
                method: "GET",
                url: `${
                    Cypress.env("credentials").CYPRESS_DISABLE.URL
                }api/remediation-actions/connections/actions/${actionID}`,
                headers: {
                    Authorization: `Token ${APIKey}`,
                },
            }).then((response) =>
                cy.get("@timezone").then((timezone) => ({
                    response: response.body,
                    timezone,
                }))
            );
        });
};

/**
 * Parse a cron expression and get the next execution time in a specific timezone
 * @param {string} cronExpression - The cron expression (e.g., "0 1 * * *")
 * @param {string} timezone - The timezone (e.g., "America/New_York")
 * @returns {Date} The next execution time in the specified timezone
 */
const parseCronExpression = (cronExpression) => {
    try {
        const interval = CronExpressionParser.parse(cronExpression, {
            tz: "UTC",
        });
        return interval.next().toDate();
    } catch (error) {
        throw new Error(`Failed to parse cron expression "${cronExpression}": ${error.message}`);
    }
};

/**
 * Parse expected time string to 24-hour format
 * @param {string} timeString - Time in format "1:00 am", "13:00", etc.
 * @returns {number} Hour in 24-hour format
 */
const parseExpectedTime = (timeString) => {
    const timeStr = timeString.toLowerCase().trim();

    // Handle 12-hour format (e.g., "1:00 am", "1:00 pm")
    if (timeStr.includes("am") || timeStr.includes("pm")) {
        const [time, period] = timeStr.split(/\s+/);
        const [hourStr] = time.split(":");
        let hour = parseInt(hourStr, 10);

        if (period === "pm" && hour !== 12) {
            hour += 12;
        } else if (period === "am" && hour === 12) {
            hour = 0;
        }

        return hour;
    }

    // Handle 24-hour format (e.g., "13:00", "01:00")
    const [hourStr] = timeStr.split(":");
    return parseInt(hourStr, 10);
};

/**
 * Validate that a cron expression will execute at the expected time in the given timezone
 * @param {string} cronExpression - The cron expression from API
 * @param {string} expectedTime - Expected time in format "1:00 am" or "13:00"
 * @param {string} timezone - The user's timezone
 * @returns {boolean} True if the cron matches the expected time
 */
const validateCronWithTimezone = (cronExpression, expectedTime, timezone) => {
    try {
        const nextExecution = new Date(parseCronExpression(cronExpression));

        const getESTHour = (utcDate) => {
            const estFormatter = new Intl.DateTimeFormat("en-US", {
                timeZone: timezone,
                hour: "numeric",
                hour12: false,
            });
            return parseInt(estFormatter.format(utcDate), 10);
        };

        const expectedHour = parseExpectedTime(expectedTime);
        const executionHourEST = getESTHour(nextExecution);

        return executionHourEST === expectedHour;
    } catch (error) {
        cy.log(`Error validating cron: ${error.message}`);
        return false;
    }
};

export {
    actionSearch,
    clearSearchedAction,
    configureServiceNowIntegration,
    createNewAction,
    deleteActionByName,
    getActionAPIResponse,
    getColumnIndexByName,
    getDatalakeSchemas,
    getFormattedNYTimePlusMinutes,
    navigateToRemediationActions,
    openCreateNewActionDialog,
    searchAndSelectAction,
    updateSnowflakeDatabase,
    validateCronWithTimezone,
    validateSchemasAndTablesInForm,
    validateServiceNowIntegrationFields,
    validateStep1FormFields,
    waitForActionStatusToBeActive,
    waitForActionToComplete,
    waitForActionToCreate,
    waitForActionToDisappearInTable,
    waitForNoDataLakeMessage,
};
