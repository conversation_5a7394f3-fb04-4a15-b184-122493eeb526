/// <reference types="cypress"/>

import {
    createNewAction,
    deleteActionByName,
    getActionAPIResponse,
    navigateToRemediationActions,
    openCreateNewActionDialog,
    validateCronWithTimezone,
    waitForActionStatusToBeActive,
} from "./remediation_action_utils";

describe.skip(
    "Create Remediation Action | Schedule Time Validate - API",
    { tags: ["remediation_actions", "full", "regression"] },
    () => {
        const createdActions = [];

        before(() => {
            cy.clearCookies();
            cy.visit(Cypress.env("credentials").CYPRESS_DISABLE.URL);
            cy.manualLogin(
                Cypress.env("credentials").CYPRESS_DISABLE.ADMIN_USERNAME,
                Cypress.env("credentials").CYPRESS_DISABLE.ADMIN_PASS
            );
        });

        it("should create new action to set Schedule Time and validate from API with tenant timezone", () => {
            const scheduleTime = "1:00 am";
            navigateToRemediationActions();
            const actionName = `scheduled-auto-test-${Date.now()}`;
            openCreateNewActionDialog();
            cy.intercept("POST", "/api/remediation-actions/connections/actions/").as("createAction");
            createNewAction(
                "ServiceNow",
                actionName,
                "CRUD Automation Test",
                "Open Cybersecurity Schema Framework (OCSF)",
                "Account Change",
                { "User ID": { IsNotNone: true }, "User Result": { IsNotNone: true } },
                "CRUD Automation Test Title",
                "CRUD Ticket Description",
                '{"table": "incident"}',
                scheduleTime,
                "All",
                true
            );
            waitForActionStatusToBeActive(actionName);
            cy.wait("@createAction").then((interception) => {
                const actionID = interception.response.body.data.action_id;
                getActionAPIResponse(actionID).then((response) => {
                    const cronExpression = response.response.schedule_interval;
                    const isValid = validateCronWithTimezone(cronExpression, scheduleTime, response.timezone);
                    expect(isValid).to.equal(true);
                });
            });
            createdActions.push(actionName);
        });

        after(() => {
            if (createdActions.length > 0) {
                cy.visit(`${Cypress.env("credentials").CYPRESS_DISABLE.URL}logout`);
                cy.manualLogin(
                    Cypress.env("credentials").CYPRESS_DISABLE.ADMIN_USERNAME,
                    Cypress.env("credentials").CYPRESS_DISABLE.ADMIN_PASS
                );
                navigateToRemediationActions();
                deleteActionByName(createdActions[0]);
                cy.clickLogout();
            }
        });
    }
);
