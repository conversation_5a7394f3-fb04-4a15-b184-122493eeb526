/// <reference types="cypress"/>
import { changeDataLakeState, navigateToDataLakeConfig } from "../configuration/config_utils";

import { waitForNoDataLakeMessage } from "./remediation_action_utils";

describe.skip(
    "Create Remediation Action | Data Lake Disabled",
    { tags: ["remediation_actions", "full", "regression"] },
    () => {
        before(() => {
            cy.clearCookies();
            cy.visit(Cypress.env("credentials").CYPRESS_DISABLE.URL);
            cy.manualLogin(
                Cypress.env("credentials").CYPRESS_DISABLE.ADMIN_USERNAME,
                Cypress.env("credentials").CYPRESS_DISABLE.ADMIN_PASS
            );
            navigateToDataLakeConfig();
            changeDataLakeState("Disable");
        });

        it("should create new action when data lake is disabled", () => {
            cy.navigateTopNavWithoutErrorChecks("Data", "Remediation Actions");
            cy.findByRole("heading", { level: 2 })
                .invoke("text")
                .then((text) => {
                    expect(text).to.equal("Actions");
                });
            cy.retryTextNotContainCheck(".alert.alert-info.fade.show", " Loading");
            cy.contains("Searching...").should("not.exist");
            cy.url().should("include", "remediation-actions");
            waitForNoDataLakeMessage();
        });

        after(() => {
            cy.visit(`${Cypress.env("credentials").CYPRESS_DISABLE.URL}logout`);
            cy.manualLogin(
                Cypress.env("credentials").CYPRESS_DISABLE.ADMIN_USERNAME,
                Cypress.env("credentials").CYPRESS_DISABLE.ADMIN_PASS
            );
            navigateToDataLakeConfig();
            changeDataLakeState("Enable");
            cy.clickLogout();
        });
    }
);
