/// <reference types="cypress"/>
import {
    clearSearchedAction,
    createNewAction,
    deleteActionByName,
    navigateToRemediationActions,
    openCreateNewActionDialog,
    searchAndSelectAction,
    waitForActionToDisappearInTable,
} from "./remediation_action_utils";

describe.skip("Tenant: Remediation Actions - Delete", { tags: ["remediation_actions", "full", "regression"] }, () => {
    const createdActions = [];

    before(() => {
        cy.clearCookies();
        cy.visit(Cypress.env("credentials").DATABEE_UI);
        cy.manualLogin(
            Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
            Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
        );
        navigateToRemediationActions();
        const actionName = `delete-auto-test-${Date.now()}`;
        openCreateNewActionDialog();
        createNewAction(
            "ServiceNow",
            actionName,
            "CRUD Automation Test",
            "Open Cybersecurity Schema Framework (OCSF)",
            "Account Change",
            { "User ID": { IsNotNone: true }, "User Result": { IsNotNone: true } },
            "CRUD Automation Test Title",
            "CRUD Ticket Description",
            '{"table": "incident"}',
            "12:00 am",
            "All",
            true
        );
        createdActions.push(actionName);
    });

    it("Verify Deletion Of New Remediation Actions", () => {
        cy.contains("Searching...").should("not.exist");
        deleteActionByName(createdActions[0]);
    });

    after(() => {
        cy.clickLogout();
        cy.clearCookies();
    });
});

describe.skip(
    "Tenant: Remediation Actions - Delete Multiple Actions",
    { tags: ["remediation_actions", "full", "regression"] },
    () => {
        const createdActions = [];

        before(() => {
            cy.clearCookies();
            cy.visit(Cypress.env("credentials").DATABEE_UI);
            cy.manualLogin(
                Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
                Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
            );
            navigateToRemediationActions();
            const actionName = `delete-auto-test-${Date.now()}`;
            [actionName.concat("-first"), actionName.concat("-second"), actionName.concat("-third")].forEach(
                (action) => {
                    openCreateNewActionDialog();
                    createNewAction(
                        "ServiceNow",
                        action,
                        "CRUD Automation Test",
                        "Open Cybersecurity Schema Framework (OCSF)",
                        "Account Change",
                        { "User ID": { IsNotNone: true }, "User Result": { IsNotNone: true } },
                        "CRUD Automation Test Title",
                        "CRUD Ticket Description",
                        '{"table": "incident"}',
                        "12:00 am",
                        "All",
                        true
                    );
                    createdActions.push(action);
                }
            );
        });

        it("Verify Deletion Of New Remediation Actions", () => {
            cy.contains("Searching...").should("not.exist");
            createdActions.forEach((action) => {
                searchAndSelectAction(action);
                clearSearchedAction();
            });
            cy.findByRole("button", { name: "Delete" }).should("be.enabled").click();
            cy.validateToastMessage("Actions deleted successfully");
            createdActions.forEach((action) => {
                waitForActionToDisappearInTable(action);
            });
        });

        after(() => {
            cy.clickLogout();
            cy.clearCookies();
        });
    }
);
