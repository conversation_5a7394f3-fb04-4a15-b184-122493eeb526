/// <reference types="cypress"/>
import createNotificationAPI from "./notification_utils";

describe("Tenant: Notifications", { tags: ["full", "notification", "empty-db"] }, () => {
    Object.entries(Cypress.env("credentials").DATABEE_USER_IDS).forEach(([UserName, UserId]) => {
        it(`${UserName} can view notification sent by Admin`, { tags: "regression" }, () => {
            cy.clearCookies();
            const testrandomString = Math.random().toString(36).substring(8);
            const newTitle = `Title${testrandomString}`;
            const newMessage = `Message${testrandomString}`;
            let userRole;
            if (`${UserName}` === "ANALYST") {
                userRole = Cypress.env("credentials").DATABEE_USER_ROLES.Analyst;
            } else if (`${UserName}` === "DATAENGINEER") {
                userRole = Cypress.env("credentials").DATABEE_USER_ROLES.DataEngineer;
            } else if (`${UserName}` === "ADMIN") {
                userRole = Cypress.env("credentials").DATABEE_USER_ROLES.Admin;
            }
            cy.visit(Cypress.env("credentials").DATABEE_UI);
            cy.manualLogin(
                Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
                Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
            );
            cy.findByTitle("My Profile").click();
            cy.findByText("My Profile").click();
            cy.get("body").then(($body) => {
                if ($body.find('button:contains("Generate API Key")').length > 0) {
                    // If Generate API Key button exists, click it
                    cy.findByRole("button", { name: "Generate API Key" }).click();
                } else {
                    // If Generate API Key doesn't exist, click Show API Key instead
                    cy.findByRole("button", { name: "Show API Key" }).click();
                }
            });

            // Check for Hide API Key button (which appears after showing)
            cy.findByRole("button", { name: "Hide API Key" }).should("be.visible");

            // Check for Revoke API Key button
            cy.findByRole("button", { name: "Revoke API Key" }).should("be.visible");

            // Find the Copy to clipboard button using a more resilient approach
            cy.findAllByText("Copy to clipboard").should("be.visible");
            cy.findByText("API Key")
                .siblings("input")
                .invoke("val")
                .then((APIKey) => {
                    createNotificationAPI(newTitle, newMessage, userRole, APIKey);
                });
            cy.findByTitle("My Profile")
                .parent()
                .within(($el) => {
                    cy.wrap($el).click();
                    cy.findByText("Logout").click();
                });
            cy.manualLogin(UserId, Cypress.env("credentials")[`DATABEE_${UserName}_PASSWORD`]);
            cy.findByTitle("Notifications").click();
            cy.findByRole("link", { name: "See All" }).click();
            cy.findByText(newTitle).should("be.visible");
            cy.findByText(newMessage).should("be.visible");
            cy.findByTitle("My Profile")
                .parent()
                .within(($el) => {
                    cy.wrap($el).click();
                    cy.findByText("Logout").click();
                });
        });
    });
});
