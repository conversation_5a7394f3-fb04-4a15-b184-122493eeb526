const createNotificationAPI = (title, message, role, APIKey) => {
    cy.request({
        method: "POST",
        url: `${Cypress.env("credentials").DATABEE_UI}api/notifications/create/`,
        headers: { Authorization: `Token ${APIKey}` },
        body: {
            title,
            message,
            role,
        },
    }).then((response) => {
        expect(response.status).to.eqls(201);
        expect(response.body.title).to.eqls(title);
        expect(response.body.message).to.eqls(message);
    });
};

export default createNotificationAPI;
