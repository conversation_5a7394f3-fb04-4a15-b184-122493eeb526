import { clickSearch, setSearchParams, setSearchTable } from "../search/search_utils";

const isWindowsEventsFeed = (feedName) => feedName === "windows_event_log";

const getDatasourceSearchCriteria = (datasourceId, feedName, outputs, windowEventCount) => {
    if (isWindowsEventsFeed(feedName)) {
        return windowEventCount.map((event) => ({
            className: "Authentication",
            datasourceId,
            feedName,
            earliestEventTime: new Date() - 1,
            count: event.minEventCount,
            eventCode: event.eventId,
        }));
    }

    const earliestEventTime = outputs.reduce((earliest, output) => {
        const itemDate = new Date(output.time);
        return earliest === null || itemDate < earliest ? itemDate : earliest;
    }, null);

    return [...new Set(outputs.map((output) => output.class_name))].map((className) => ({
        className,
        datasourceId,
        feedName,
        earliestEventTime,
        count: outputs.filter((output) => output.class_name === className).length,
        eventCode: null,
    }));
};

const addParameterIfNeeded = (paramName) => {
    // First check if data parameter already exists
    cy.get("body").then(($body) => {
        const dataExists = $body.find(`.react-select__placeholder:contains("${paramName}")`).length > 0;

        if (!dataExists) {
            cy.log("Metadata parameter not found, adding it...");

            cy.findByText("Add Parameter")
                .should("exist")
                .closest(".react-select__control")
                .should("exist")
                .within(() => {
                    cy.selectOption('input[role="combobox"]', paramName);
                });

            // Verify by checking if any placeholder contains "Metadata:" text
            // Instead of checking all placeholders at once
            cy.get(".react-select__placeholder").each(($placeholder) => {
                const text = $placeholder.text().trim();
                if (text.includes(paramName)) {
                    cy.log(`Successfully found ${paramName} parameter`);
                    return false;
                }
                return true;
            });
        } else {
            cy.log(`${paramName} parameter already exists, skipping addition`);
        }
    });
};

const performDatasourceEventSearch = (criteria, setCount) => {
    const { className, datasourceId, feedName, earliestEventTime, count, eventCode } = criteria;
    cy.navigateTopNav("Search");
    cy.retryTextNotContainCheck(".alert", "Loading tables...");
    setSearchTable("Search For:", className);
    cy.findByText("Loading Histogram...").should("not.exist");
    cy.findByText("Searching...").should("not.exist");
    let metadata = [
        {
            Key: "data_source",
            ValueIn: datasourceId,
        },
    ];

    if (isWindowsEventsFeed(feedName)) {
        metadata = [
            ...metadata,
            {
                Key: "event_code",
                ValueIn: eventCode,
            },
        ];
    }

    // Event time from Feed Output JSON file.
    const todaysDate = new Date().getTime();
    // Event time from Feed Output JSON file.
    const actualEventTime = new Date(earliestEventTime).getTime();
    // Getting last N seconds to search adding a delay of a day in seconds(86400)
    const lastNSeconds = Math.ceil((todaysDate - actualEventTime) / 1000 + 86400);

    // Add those parameters which are not present in the dropdown.
    addParameterIfNeeded("Metadata");

    setSearchParams({
        "Event Time:": { Seconds: Math.ceil(lastNSeconds) },
        "Metadata:": { Metadata: metadata },
    });

    clickSearch();
    cy.findByText("Loading Histogram...").should("not.exist");
    cy.findByText("Searching...").should("not.exist");
    // After search UI shows number of total searched result
    // eg : Showing 1 to 25 of 4,082 results.
    // Fetching total searched count from string
    cy.get('[id="search-entities"]')
        .find(".container-fluid")
        .children()
        .children()
        .find("span")
        .then(($ele) => cy.wrap($ele.text().split("of").pop().split("results.")[0]).as("result"));

    cy.get("@result").then((result) => {
        const actualCount = parseInt(result.replaceAll(",", ""), 10);
        setCount(feedName, "success", actualCount);
        if (actualCount < count) {
            setCount(feedName, "failed", count - actualCount);
        }
    });
};

export { addParameterIfNeeded, getDatasourceSearchCriteria, performDatasourceEventSearch };
