/// <reference types="cypress"/>

import { getDatasourceSearchCriteria, performDatasourceEventSearch } from "./feeds_utils";

// For Windows Security events feed we're asserting minimum event counts to validate
// Since these events are not controlled via data collector ingestion.
// In a particular session, the defined count denotes the minimum events which should have
// been generated by widnows system during the whole test suite execution.
const windowEventCount = [
    { eventId: 4624, minEventCount: 150 },
    { eventId: 4634, minEventCount: 40 },
    { eventId: 4648, minEventCount: 50 },
];

describe("Tenant: Search datasource events from data collector", { tags: ["full", "data"] }, () => {
    beforeEach(() => {
        cy.clearCookies();
        cy.visit(Cypress.env("credentials").DATABEE_UI);
        cy.manualLogin(
            Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
            Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
        );
    });

    // Guarding the test to run only if collectorName is provided
    // collectorName is only provided for data collector e2e tests
    // these tests are not expected to run from cron workflow in this repo
    if (Cypress.env("collectorName")) {
        const feeds = Cypress.config("feeds");
        const eventsSummary = {};
        let excludedFiles = 0;

        const setCount = (feedName, key, count) => {
            eventsSummary[feedName][key] += count;
        };

        // Guarding the test to run only if feeds are provided
        if (Object.keys(feeds).length > 0) {
            Object.entries(feeds).forEach(([feedName, feedObject]) => {
                eventsSummary[feedName] = {
                    total_sample: 0,
                    success: 0,
                    failed: 0,
                };
                const isWindowsEventsFeed = feedName === "windows_event_log";
                it(`Perform event search for feed ${feedName}`, { tags: ["e2e_tests"] }, () => {
                    const { datasources, outputs } = feedObject;
                    eventsSummary[feedName].total_sample = isWindowsEventsFeed
                        ? windowEventCount.reduce((acc, val) => acc + val.minEventCount, 0) * datasources.length
                        : outputs.length * datasources.length;
                    datasources.forEach((ds) => {
                        const { datasourceId } = ds;

                        if (ds.logSource === "Flat File" && ds?.exclusionFiles) {
                            excludedFiles += ds.exclusionFiles[Cypress.env("os").toString().toLowerCase()].length;
                            eventsSummary[feedName].total_sample -= excludedFiles;
                        }
                        // Get search criteria for each datasource
                        const searchCriterias = getDatasourceSearchCriteria(
                            datasourceId,
                            feedName,
                            outputs,
                            windowEventCount
                        );
                        // Perform search for each criteria and set the count of events retrived from table
                        searchCriterias.forEach((criteria) => performDatasourceEventSearch(criteria, setCount));
                    });
                    // Failing test case if there are any failure event counts.
                    // Gracefully handling the failure scenario so that the event summary can still be processed.
                    eventsSummary[feedName].failed -= excludedFiles;
                    cy.wrap(eventsSummary[feedName]).then((summary) => {
                        if (summary.failed > 0) {
                            cy.fail(
                                `Expected events were not received for feed: ${feedName}, failed events: ${summary.failed}`
                            );
                        }
                    });
                });
            });
        }

        after(() => {
            // Store the search results summary in a file to be printed as a report
            // It is a separate test to ensure that the file is created after all the search tests are executed
            cy.writeFile("cypress/context/search_assertion_results.json", eventsSummary);
        });
    }
});
