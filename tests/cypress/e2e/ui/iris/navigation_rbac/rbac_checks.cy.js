/// <reference types="cypress"/>
import {
    createNewDataSourceWithoutSubmitForm,
    gotoAddNewDataSourcePage,
    waitForDataFeedToDelete,
} from "../data_feed/data_utils";
import { clickSearch, getSearchTables, setSearchParams, setSearchTable } from "../search/search_utils";

import {
    accessCompliance,
    accessManagementTab,
    accessMyProfile,
    accessNotifications,
    accessSystemtab,
    configNotExist,
    gotoDataSource,
    goToHelpLinks,
    noAccessToDataSource,
    overlayDashboard,
    verifyDashboard,
} from "./navigation_utils";

describe("Admin User - Full Access Verification", { tags: ["demo_staging", "smoke"] }, () => {
    const adminUser = Cypress.env("credentials").DATABEE_USER_IDS.ADMIN;
    const adminPass = Cypress.env("credentials").DATABEE_ADMIN_PASSWORD;

    before(() => {
        cy.clearCookies();
        cy.visit(Cypress.env("credentials").DATABEE_UI);
        cy.manualLogin(adminUser, adminPass);
    });

    afterEach(() => {
        cy.clickLogout();
    });

    it("Admin user - complete access and functionality validation", () => {
        // Access Compliance Page
        accessCompliance();

        // Access System and Management Tabs in Configuration
        accessSystemtab();
        accessManagementTab();

        // Access Dashboard Overview
        cy.retryTextNotContainCheck(".alert.alert-primary.fade.show", "Loading...");
        cy.navigateTopNav("Console", "Overview");
        cy.retryTextNotContainCheck(".alert.alert-primary.fade.show", "Loading...");
        verifyDashboard();
        overlayDashboard();

        // Access Help Links Page
        goToHelpLinks();

        // Access My Profile Page
        accessMyProfile();

        // Access Notifications Page
        accessNotifications();

        // Active Detections Page
        cy.navigateTopNav("Security", "Detection Streams");
        setSearchParams({ "Created Date:": { Months: 2 } });
        cy.findByRole("button", { name: "Apply" }).click();
        cy.findAllByRole("table").find("tbody").find("tr").should("have.length.greaterThan", 0);
        cy.get("body").then(($body) => {
            if ($body.find('[role="alert"]').length > 0) {
                cy.get('[role="alert"]').should("not.have.class", "alert-danger");
            }
        });

        // Detection Chains Page
        cy.navigateTopNav("Security", "Detection Chains");
        setSearchParams({ "Last Modified Time:": { Months: 2 } });
        cy.findByRole("button", { name: "Apply" }).click();
        cy.findAllByRole("table").find("tbody").find("tr").should("have.length.greaterThan", 0);
        cy.get("body").then(($body) => {
            if ($body.find('[role="alert"]').length > 0) {
                cy.get('[role="alert"]').should("not.have.class", "alert-danger");
            }
        });

        // DataBee Findings Page
        cy.navigateTopNav("Security", "DataBee Findings");
        setSearchParams({ "Event Time:": { Months: 2 } });
        clickSearch();
        cy.findAllByRole("table").find("tbody").find("tr").should("have.length.greaterThan", 0);
        cy.get("body").then(($body) => {
            if ($body.find('[role="alert"]').length > 0) {
                cy.get('[role="alert"]').should("not.have.class", "alert-danger");
            }
        });

        // Suppression List Page
        cy.navigateTopNav("Security", "Suppress List");
        cy.findByText("Add Suppression").click();
        cy.contains("Add Suppression").should("be.visible");
        cy.findByLabelText("Close").click();
        cy.findByRole("table").then(($table) => {
            if ($table.find(".rt-tbody").length === 0 || $table.text().includes("No configured suppressions")) {
                cy.findByText("No configured suppressions").should("be.visible");
            } else {
                cy.findByRole("table").within(() => {
                    cy.findAllByRole("columnheader").then(($headers) => {
                        cy.wrap($headers.eq(0)).should("contain.text", "Action");
                        cy.wrap($headers.eq(1)).should("contain.text", "End Suppress On");
                        cy.wrap($headers.eq(2)).should("contain.text", "Description");
                        cy.wrap($headers.eq(3)).should("contain.text", "Devices");
                        cy.wrap($headers.eq(4)).should("contain.text", "Users");
                        cy.wrap($headers.eq(5)).should("contain.text", "Features");
                        cy.wrap($headers.eq(6)).should("contain.text", "Severity IDs");
                        cy.wrap($headers.eq(7)).should("contain.text", "Hits");
                    });
                });
            }
        });
    });
});

describe("Access Control - Analyst User", { tags: ["demo_staging", "smoke"] }, () => {
    before(() => {
        cy.clearCookies();
        cy.visit(Cypress.env("credentials").DATABEE_UI);
    });
    it("Analyst user cannot access Data Feed and configuration page", () => {
        cy.manualLogin(
            Cypress.env("credentials").DATABEE_USER_IDS.ANALYST,
            Cypress.env("credentials").DATABEE_ANALYST_PASSWORD
        );
        noAccessToDataSource();
        configNotExist();
    });
});

describe("Access Control - Data Engineer User", { tags: ["demo_staging", "smoke"] }, () => {
    before(() => {
        cy.clearCookies();
        cy.visit(Cypress.env("credentials").DATABEE_UI);
    });
    it("Data Engineer user access Data Feed and cannot access configuration page", () => {
        cy.manualLogin(
            Cypress.env("credentials").DATABEE_USER_IDS.DATAENGINEER,
            Cypress.env("credentials").DATABEE_DATAENGINEER_PASSWORD
        );
        gotoDataSource();
        configNotExist();
    });
});

describe("Data Feeds", { tags: ["smoke", "full", "data"] }, () => {
    before(() => {
        cy.clearCookies();
        cy.visit(Cypress.env("credentials").DATABEE_UI);
    });
    it("API Ingest Feed - Basic Flow", () => {
        cy.manualLogin(
            Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
            Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
        );
        gotoAddNewDataSourcePage();
        const { sourceName } = createNewDataSourceWithoutSubmitForm("ServiceNow CMDB", "API Ingest", "us-east-1");
        cy.navigateTopNav("Data", "Data Feeds");
        cy.findByPlaceholderText("Search Data Feeds").click({ force: true }).type(sourceName);
        cy.get(".status-indicator-state").should("contain.text", "In Progress");
        cy.findByText(sourceName)
            .parent()
            .within(($el) => {
                cy.wrap($el).click({ force: true });
            });
        cy.findByTitle("Delete").click();
        cy.on("window:alert", () => true);
        cy.contains("Delete Data Feed");
        cy.findByRole("button", { name: "Delete Data Feed" }).click();
        cy.contains(`Deleting ${sourceName}.`).should("be.visible");
        cy.findByRole("button", { name: "Clear" }).click({ force: true });
        waitForDataFeedToDelete(sourceName);
    });
});

describe("Search Page Access", { tags: ["search", "data_faker"] }, () => {
    before(() => {
        cy.clearCookies();
        cy.visit(Cypress.env("credentials").DATABEE_UI);
    });

    afterEach(() => {
        cy.clickLogout();
    });

    it("should load the Device search table and display data rows successfully", { tags: "regression" }, () => {
        cy.manualLogin(
            Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
            Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
        );
        cy.navigateTopNav("Search");
        cy.retryTextNotContainCheck(".alert.alert-info.fade.show", " Loading tables...");
        setSearchTable("Search For:", "Device");
        const filterParams = {
            "Modified Time:": { Months: 2 },
        };
        setSearchParams(filterParams);
        clickSearch();
        cy.findByText("Loading Histogram...").should("not.exist");
        cy.findByText("Searching...").should("not.exist");
        cy.findAllByRole("table").find("tbody").find("tr").should("have.length.greaterThan", 0);
    });

    it("Admin can access search pages", { tags: ["demo_staging"] }, () => {
        cy.manualLogin(
            Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
            Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
        );
        cy.navigateTopNav("Search");
        cy.retryTextNotContainCheck(".alert.alert-info.fade.show", " Searching...");
        const failedOptions = [];
        getSearchTables().then((searchTableList) => {
            searchTableList.forEach((searchTable) => {
                setSearchTable("Search For:", searchTable);
                cy.retryTextNotContainCheck(".alert", "Loading table configuration...");
                cy.findByText("Loading Histogram...").should("not.exist");
                cy.findByText("Searching...").should("not.exist");
                cy.get("body").then(($body) => {
                    const alert = $body.find('[role="alert"]');
                    if (alert.length > 0 && alert.hasClass("alert-danger")) {
                        failedOptions.push(searchTable);
                    }
                });
            });
        });

        cy.then(() => {
            if (failedOptions.length > 0) {
                throw new Error(`The following options caused errors: ${failedOptions.join(", ")}`);
            }
        });

        cy.clickLogout();
    });
});
