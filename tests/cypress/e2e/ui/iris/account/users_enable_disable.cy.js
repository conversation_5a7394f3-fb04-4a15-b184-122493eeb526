/// <reference types="cypress"/>

import { accessUsers, verifyDashboard } from "../navigation_rbac/navigation_utils";

import { disableUser, enableUser, logoutWithinTest } from "./account_utils";

describe(
    "Tenant: Configuration -> Access Management Admin user can Disable and Enable user",
    { tags: ["config_user", "full", "empty-db"] },
    () => {
        beforeEach(() => {
            cy.clearCookies();
            cy.visit(Cypress.env("credentials").DATABEE_UI);
        });

        afterEach(() => {
            cy.clickLogout();
        });

        it(
            "Admin user can deactivate user and make sure user not able to login in DataBee portal",
            { tags: "regression" },
            () => {
                cy.manualLogin(
                    Cypress.env("credentials").DATABEE_USER_TEST_IDS.ADMIN_USER_TEST,
                    Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
                );
                accessUsers();
                const userEmail = Cypress.env("credentials").DATABEE_USER_TEST_IDS.ANALYST_USER_TEST;
                cy.findByText("Username").next("input").click().type(userEmail);
                cy.get("body").as("body");
                cy.findByRole("cell", { name: userEmail })
                    .parent("tr")
                    .within(($tr) => {
                        if ($tr.text().includes("Inactive")) {
                            cy.wrap($tr).findByRole("button", { name: "Enable" }).should("be.enabled").click();
                            cy.get("@body")
                                .findByRole("dialog")
                                .within(() => {
                                    cy.findByText("Yes").click();
                                });
                        }
                    });
                cy.findByText("Username").next("input").clear();
                disableUser(Cypress.env("credentials").DATABEE_USER_TEST_IDS.ANALYST_USER_TEST);
                logoutWithinTest();
                cy.manualLogin(
                    Cypress.env("credentials").DATABEE_USER_TEST_IDS.ANALYST_USER_TEST,
                    Cypress.env("credentials").DATABEE_ANALYST_PASSWORD
                );
                cy.findByRole("button", { name: "Login" }).should("be.visible");
                cy.findByText("Invalid username or password").should("be.visible").and("exist");
                cy.manualLogin(
                    Cypress.env("credentials").DATABEE_USER_TEST_IDS.ADMIN_USER_TEST,
                    Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
                );
                accessUsers();
                enableUser(Cypress.env("credentials").DATABEE_USER_TEST_IDS.ANALYST_USER_TEST);
                logoutWithinTest();
                cy.manualLogin(
                    Cypress.env("credentials").DATABEE_USER_TEST_IDS.ANALYST_USER_TEST,
                    Cypress.env("credentials").DATABEE_ANALYST_PASSWORD
                );
                verifyDashboard();
            }
        );

        it(
            "Cluster Admin user can deactivate user and make sure user not able to login in DataBee portal",
            { tags: "regression" },
            () => {
                cy.manualLogin(
                    Cypress.env("credentials").CLUSTER_USER_TEST_IDS.ADMIN_USER_TEST,
                    Cypress.env("credentials").CLUSTER_ADMIN_PASSWORD
                );
                accessUsers();
                const userEmail = Cypress.env("credentials").DATABEE_USER_TEST_IDS.ANALYST_USER_TEST;
                cy.findByText("Username").next("input").click().type(userEmail);
                cy.get("body").as("body");
                cy.findByRole("cell", { name: userEmail })
                    .parent("tr")
                    .within(($tr) => {
                        if ($tr.text().includes("Inactive")) {
                            cy.wrap($tr).findByRole("button", { name: "Enable" }).should("be.enabled").click();
                            cy.get("@body")
                                .findByRole("dialog")
                                .within(() => {
                                    cy.findByText("Yes").click();
                                });
                        }
                    });
                cy.findByText("Username").next("input").clear();
                disableUser(Cypress.env("credentials").DATABEE_USER_TEST_IDS.ANALYST_USER_TEST);
                logoutWithinTest();
                cy.manualLogin(
                    Cypress.env("credentials").DATABEE_USER_TEST_IDS.ANALYST_USER_TEST,
                    Cypress.env("credentials").DATABEE_ANALYST_PASSWORD
                );
                cy.findByRole("button", { name: "Login" }).should("be.visible");
                cy.findByText("Invalid username or password").should("be.visible").and("exist");
                cy.manualLogin(
                    Cypress.env("credentials").CLUSTER_USER_TEST_IDS.ADMIN_USER_TEST,
                    Cypress.env("credentials").CLUSTER_ADMIN_PASSWORD
                );
                accessUsers();
                enableUser(Cypress.env("credentials").DATABEE_USER_TEST_IDS.ANALYST_USER_TEST);
                logoutWithinTest();
                cy.manualLogin(
                    Cypress.env("credentials").DATABEE_USER_TEST_IDS.ANALYST_USER_TEST,
                    Cypress.env("credentials").DATABEE_ANALYST_PASSWORD
                );
                verifyDashboard();
            }
        );

        it(
            "Cluster Admin user can deactivate Tenant Admin and make sure user not able to login in DataBee portal",
            { tags: "regression" },
            () => {
                cy.manualLogin(
                    Cypress.env("credentials").CLUSTER_USER_TEST_IDS.ADMIN_USER_TEST,
                    Cypress.env("credentials").CLUSTER_ADMIN_PASSWORD
                );
                accessUsers();
                const userEmail = Cypress.env("credentials").DATABEE_USER_TEST_IDS.ADMIN_USER_TEST;
                cy.findByText("Username").next("input").click().type(userEmail);
                cy.get("body").as("body");
                cy.findByRole("cell", { name: userEmail })
                    .parent("tr")
                    .within(($tr) => {
                        if ($tr.text().includes("Inactive")) {
                            cy.wrap($tr).findByRole("button", { name: "Enable" }).should("be.enabled").click();
                            cy.get("@body")
                                .findByRole("dialog")
                                .within(() => {
                                    cy.findByText("Yes").click();
                                });
                        }
                    });
                cy.findByText("Username").next("input").clear();
                disableUser(Cypress.env("credentials").DATABEE_USER_TEST_IDS.ADMIN_USER_TEST);
                logoutWithinTest();
                cy.manualLogin(
                    Cypress.env("credentials").DATABEE_USER_TEST_IDS.ADMIN_USER_TEST,
                    Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
                );
                cy.findByRole("button", { name: "Login" }).should("be.visible");
                cy.findByText("Invalid username or password").should("be.visible").and("exist");
                cy.manualLogin(
                    Cypress.env("credentials").CLUSTER_USER_TEST_IDS.ADMIN_USER_TEST,
                    Cypress.env("credentials").CLUSTER_ADMIN_PASSWORD
                );
                accessUsers();
                enableUser(Cypress.env("credentials").DATABEE_USER_TEST_IDS.ADMIN_USER_TEST);
                logoutWithinTest();
                cy.manualLogin(
                    Cypress.env("credentials").DATABEE_USER_TEST_IDS.ADMIN_USER_TEST,
                    Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
                );
                verifyDashboard();
            }
        );
    }
);
