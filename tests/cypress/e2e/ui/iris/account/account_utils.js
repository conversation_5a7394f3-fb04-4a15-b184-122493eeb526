// <reference types="cypress"/>

// Methods

export function verifyManageUserAccountsUI() {
    const tableHeaders = ["ID", "Full Name", "Username", "Last Login", "Created", "Role", "Status", "Actions"];
    tableHeaders.forEach((item) => {
        cy.findByText(item).should("be.visible");
    });
}

export function logoutWithinTest() {
    cy.findByTitle("My Profile")
        .parent()
        .within(($el) => {
            cy.wrap($el).click();
            cy.findByText("Logout").click();
        });
}

export function createUser(userRole, userFirstName, userLastName, userEmail) {
    const testRandomString = Math.random().toString(36).substring(7);
    const firstName = userFirstName || `testFirstName${testRandomString}`;
    const lastName = userLastName || `testLastName${testRandomString}`;
    const email = userEmail || `testemail${testRandomString}@bluvector.io`;
    const role = userRole;

    cy.findByRole("button", { name: "Create User Account" }).click();
    cy.findByText("First Name").click().type(firstName);
    cy.findByText("Last Name").click().type(lastName);
    cy.findByText("Email").click().type(email);
    cy.findByText("Username").siblings("input").should("have.value", email);
    cy.findByText("Role").siblings("select").select(userRole);
    cy.findByRole("button", { name: "Submit" }).click();
    return { firstName, lastName, email, role };
}

export function verifyCreatedUser(firstName, lastName, email, role) {
    cy.findByRole("table").find("tbody").find("tr").last().should("be.visible");
    cy.get(":nth-child(3) > .form-control").type(email);
    cy.findByRole("cell", { name: email })
        .should("be.visible")
        .parent("tr")
        .within(() => {
            cy.findByRole("cell", { name: `${firstName} ${lastName}` }).should("be.visible");
            cy.findByRole("cell", { name: role }).should("be.visible");
            cy.findByText("Active").should("be.visible");
        });
}

export function disableUser(userEmail) {
    cy.get("input").last().click().type(userEmail);
    cy.findByRole("table").find("tbody").find("tr").last().should("be.visible");
    cy.findByRole("cell", { name: userEmail })
        .parent("tr")
        .within(() => {
            cy.findByRole("button", { name: "Disable" }).scrollIntoView().click();
        });
    cy.findByText("Yes").click();
    cy.findByRole("table").find("tbody").find("tr").last().should("be.visible");
    cy.get("input").last().click().type(userEmail);
    cy.findByRole("cell", { name: userEmail })
        .parent("tr")
        .within(() => {
            cy.findByText("Inactive").scrollIntoView().should("be.visible");
            cy.findByRole("button", { name: "Enable" }).should("exist");
        });
}

export function enableUser(userEmail) {
    cy.get("input").last().click().type(userEmail);
    cy.findByRole("table").find("tbody").find("tr").last().should("be.visible");
    cy.findByRole("cell", { name: userEmail })
        .parent("tr")
        .within(() => {
            cy.findByRole("button", { name: "Enable" }).scrollIntoView().click();
        });
    cy.findByText("Yes").click();
    cy.findByRole("table").find("tbody").find("tr").last().should("be.visible");
    cy.get("input").last().click().type(userEmail);
    cy.findByRole("cell", { name: userEmail })
        .parent("tr")
        .within(() => {
            cy.findByText("Active").scrollIntoView().should("be.visible");
            cy.findByRole("button", { name: "Disable" }).scrollIntoView().should("exist");
        });
}

export function retryLoginWithReload(tenantName, maxRetries = 30, attempt = 1, waitSecs = 2) {
    if (attempt > maxRetries) {
        throw new Error(`Access to ${tenantName} issue persisted after ${maxRetries} retries.`);
    }

    cy.manualLogin(
        Cypress.env("credentials").CYPRESS_DISABLE.ADMIN_USERNAME,
        Cypress.env("credentials").CYPRESS_DISABLE.ADMIN_PASS
    );

    cy.document().then((doc) => {
        const bodyText = doc.body.textContent || "";
        if (bodyText.includes(`Access to ${tenantName} data is disabled`)) {
            cy.wait(waitSecs * 1000); // if allowed, else convert to promise delay
            cy.reload().then(() => {
                retryLoginWithReload(tenantName, maxRetries, attempt + 1);
            });
        } else {
            cy.log("Login successful or access issue not present.");
        }
    });
}
