/// <reference types="cypress"/>

import { accessUsers, configNotExist } from "../navigation_rbac/navigation_utils";

import { verifyManageUserAccountsUI } from "./account_utils";

describe(
    "Tenant: Configuration -> Access Management Users can access Manage User Accounts and User details pages",
    { tags: ["smoke", "config_user", "full"] },
    () => {
        beforeEach(() => {
            cy.clearCookies();
            cy.visit(Cypress.env("credentials").DATABEE_UI);
        });

        afterEach(() => {
            cy.clickLogout();
        });

        it(
            "[C2] Admin user can access and verify Manage User Accounts and User details page UI objects",
            { tags: "smoke" },
            () => {
                cy.manualLogin(
                    Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
                    Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
                );
                accessUsers();
                verifyManageUserAccountsUI();
            }
        );

        it("[C3] Analyst user can not Create a DataBee user", { tags: "smoke" }, () => {
            cy.manualLogin(
                Cypress.env("credentials").DATABEE_USER_IDS.ANALYST,
                Cypress.env("credentials").DATABEE_ANALYST_PASSWORD
            );
            configNotExist();
        });

        it("[C4] Data Engineer user can not Create a DataBee user", { tags: "smoke" }, () => {
            cy.manualLogin(
                Cypress.env("credentials").DATABEE_USER_IDS.DATAENGINEER,
                Cypress.env("credentials").DATABEE_DATAENGINEER_PASSWORD
            );
            configNotExist();
        });
    }
);
