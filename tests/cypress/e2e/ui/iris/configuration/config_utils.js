/// <reference types="cypress"/>

const navigateToDataLakeConfig = () => {
    cy.navigateTopRightNav("Configuration", "System");
    cy.findByText(/Select your preferred data lake for data output/).should("be.visible");
};

const accessConfig = () => {
    navigateToDataLakeConfig();
    cy.get("body").then(($body) => {
        if ($body.find(".fa-circle-check").length) {
            cy.get(".fa-circle-check").click();
            cy.setInput("Enabled *", "uncheck");
            cy.findByRole("button", { name: "Submit" }).click();
            cy.findByText("Configuration updated successfully.").should("be.visible");
        }
    });
};

const fillDataLakeForm = (datalake) => {
    cy.contains(`${datalake}`).scrollIntoView().should("be.visible");

    const snowflakeFields = [
        { label: "Account Identifier *", value: Cypress.env("credentials").SNOWFLAKE_CREDENTIALS.ACCOUNT_IDENTIFIER },
        { label: "Username *", value: Cypress.env("credentials").SNOWFLAKE_CREDENTIALS.USERNAME },
        { label: "Database *", value: Cypress.env("credentials").SNOWFLAKE_CREDENTIALS.DATABASE },
        { label: "Warehouse *", value: Cypress.env("credentials").SNOWFLAKE_CREDENTIALS.WAREHOUSE },
        { label: "Role *", value: Cypress.env("credentials").SNOWFLAKE_CREDENTIALS.ROLE },
        { label: "Private Key *", value: Cypress.env("credentials").SNOWFLAKE_CREDENTIALS.PRIVATE_KEY },
        { label: "Private Key Password", value: Cypress.env("credentials").SNOWFLAKE_CREDENTIALS.PASSWORD },
    ];

    snowflakeFields.forEach(({ label, value }) => {
        cy.findAllByLabelText(label).click().clear().type(value);
    });
};

const testConnection = (datalake, setSubmit) => {
    cy.findByAltText(datalake).click();
    fillDataLakeForm(datalake);

    if (setSubmit === undefined) {
        cy.findByRole("button", { name: "Test Connectivity" }).click();
    } else {
        cy.setInput("Enabled *");
        cy.findByRole("button", { name: "Submit" }).click();
    }
};

const changeDataLakeState = (state) => {
    if (state === "Enable") {
        cy.get("body").then(($body) => {
            if ($body.find(".fa-circle-xmark").length) {
                cy.get(".fa-circle-xmark").click();
                cy.setInput("Enabled *", null);
                cy.findByRole("button", { name: "Submit" }).click();
                cy.findByText("Configuration updated successfully.").should("be.visible");
            }
        });
    } else {
        cy.get("body").then(($body) => {
            if ($body.find(".fa-circle-check").length) {
                cy.get(".fa-circle-check").click();
                cy.setInput("Enabled *", "uncheck");
                cy.findByRole("button", { name: "Submit" }).click();
                cy.findByText("Configuration updated successfully.").should("be.visible");
            }
        });
    }
};

const setSessionTimeout = (timeInSecs) => {
    cy.navigateTopRightNav("Configuration", "Access Management");
    cy.findByRole("link", { name: "Session Management" }).click();
    cy.contains("h2", "Session Management").should("be.visible");
    cy.findByRole("button", { name: "Submit" }).should("be.disabled");
    cy.findAllByLabelText("Timeout Seconds *").click().clear().type(timeInSecs);
    cy.findByRole("button", { name: "Submit" }).should("be.enabled").click();
    cy.findByText("Configuration updated successfully.").should("be.visible");
};

const DATALAKE = {
    SNOWFLAKE: "Snowflake",
};

/**
 * Validates that required form fields show and clear the "Required!" error message.
 * For each field, clears the input to trigger the error, checks for the error message,
 * then re-enters the value and ensures the error disappears.
 *
 * @param {Array<{label: string, value: string}>} fields - Array of field objects with label and value.
 */
const validateRequiredFields = (fields) => {
    fields.forEach(({ label, value }) => {
        cy.findAllByLabelText(label).click().clear();
        cy.findByText("Required!").should("be.visible");
        cy.findAllByLabelText(label).click().type(value);
        cy.findByText("Required!").should("not.exist");
    });
};

/**
 * Populates the "Private Key" and "Private Key Password" fields in the form.
 * Validates that the "Required!" error appears when the private key is empty,
 * then enters the provided private key and password, ensuring the error disappears.
 *
 * @param {string} privateKey - The private key to enter.
 * @param {string} password - The password for the private key.
 */
const populatePrivateKeyAndPassword = (privateKey, password) => {
    cy.findAllByLabelText("Private Key *").should("not.be.empty").click();
    cy.findAllByLabelText("Private Key *").should("have.value", "");
    cy.findByText("Required!").should("be.visible");
    cy.findAllByLabelText("Private Key *").click().type(privateKey);
    cy.findByText("Required!").should("not.exist");

    cy.findAllByLabelText("Private Key Password").invoke("val").should("not.be.empty");
    cy.findAllByLabelText("Private Key Password").click().type(password);
};

/**
 * Verifies that all connection tests have passed and closes the modal.
 * Checks for the success message, clicks the "Back" button, closes the dialog,
 * and ensures the Snowflake configuration details are no longer visible.
 */
const allDatalakeConfigurationConnectionTestsPassed = () => {
    cy.findByText("All connection tests passed!").should("be.visible");
    cy.findByRole("button", { name: "Back" }).click();
    cy.findByText(/Snowflake account details can be configured here./).should("not.exist");
};

/**
 * Configures a Snowflake data lake connection using the provided credentials.
 * Fills out the form, validates required fields, populates the private key and password,
 * tests the connectivity, enables the connection, saves the configuration,
 * and verifies that all connection tests have passed.
 *
 * @param {Object} credentials - The Snowflake credentials.
 * @param {string} credentials.ACCOUNT_IDENTIFIER - The Snowflake account identifier.
 * @param {string} credentials.USERNAME - The Snowflake username.
 * @param {string} credentials.DATABASE - The Snowflake database name.
 * @param {string} credentials.WAREHOUSE - The Snowflake warehouse name.
 * @param {string} credentials.ROLE - The Snowflake role.
 * @param {string} credentials.PRIVATE_KEY - The private key for authentication.
 * @param {string} credentials.PASSWORD - The password for the private key.
 */
const configureSnowflakeConnection = (credentials) => {
    fillDataLakeForm("Snowflake");

    const fields = [
        { label: "Account Identifier *", value: credentials.ACCOUNT_IDENTIFIER },
        { label: "Username *", value: credentials.USERNAME },
        { label: "Database *", value: credentials.DATABASE },
        { label: "Warehouse *", value: credentials.WAREHOUSE },
        { label: "Role *", value: credentials.ROLE },
    ];
    validateRequiredFields(fields);
    populatePrivateKeyAndPassword(credentials.PRIVATE_KEY, credentials.PASSWORD);

    cy.findByRole("button", { name: "Test Connectivity" }).click();
    cy.contains("Connection to Snowflake successful", { timeout: 60000 }).should("exist");

    cy.setInput("Enabled *");
    cy.findByRole("button", { name: "Save" }).click();
    cy.contains("Configuration updated successfully.", { timeout: 60000 }).should("exist");

    allDatalakeConfigurationConnectionTestsPassed();
};

export {
    accessConfig,
    allDatalakeConfigurationConnectionTestsPassed,
    changeDataLakeState,
    configureSnowflakeConnection,
    DATALAKE,
    fillDataLakeForm,
    navigateToDataLakeConfig,
    setSessionTimeout,
    testConnection,
};
