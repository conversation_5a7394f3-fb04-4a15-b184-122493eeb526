/// <reference types="cypress"/>

import { setSessionTimeout } from "./config_utils";

describe("Tenant: Configuration -> Access Management -> Session Management", { tags: ["config", "full"] }, () => {
    before(() => {
        cy.clearCookies();
        cy.visit(Cypress.env("credentials").DATABEE_UI);
    });

    it("Admin User | Update Session to 60 Seconds: Validate User Logged out", { tags: "regression" }, () => {
        cy.manualLogin(
            Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
            Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
        );
        setSessionTimeout(60);
        cy.findByRole("alert").find("span").should("contain.text", "Your session will expire");
        cy.checkElementIsVisible(".alert.alert-danger.fade.show", 65);
        cy.document().its("contentType").should("include", "text/html");
        cy.get("body").should("not.be.empty");
        cy.manualLogin(
            Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
            Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
        );
        setSessionTimeout(1800);
    });

    after(() => {
        cy.clickLogout();
    });
});
