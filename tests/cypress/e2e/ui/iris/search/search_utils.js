// Methods
const setSearchTable = (label, searchTable) => {
    cy.get(".react-select__single-value")
        .invoke("text")
        .then((textContent) => {
            if (!textContent.includes(searchTable)) {
                cy.setInput(label, searchTable);
            }
        });
};

const openSelect = (fieldValue) => {
    cy.findAllByText(new RegExp(`^${fieldValue}`), { exact: false })
        .filter(".react-select__placeholder")
        .each(($el) => {
            cy.wrap($el).scrollIntoView().should("be.visible").click({ force: true });
        });
};

const setSearchParams = (searchParams) => {
    const timeUnits = ["Seconds", "Minutes", "Hours", "Days", "Weeks", "Months"];
    const jsonPopupTitle = "Choose one or more keys on this JSON field to search for.";

    // Handle JSON key-value pair search
    const handleJsonSearch = (field, value) => {
        if (field === "IsNone" || field === "IsNotNone") {
            cy.setInput(field === "IsNone" ? "Is None" : "Is Not None", value === true ? undefined : value);
        } else {
            value.forEach((json) => {
                Object.entries(json).forEach(([jsonKey, jsonValue]) => {
                    const inputValue = jsonValue || "";
                    let placeholderTxt;

                    switch (jsonKey) {
                        case "ValueNotIn":
                            placeholderTxt = "Pick a value to search not in";
                            break;
                        case "Key":
                            placeholderTxt = "Choose a key to search";
                            break;
                        case "Contains":
                            placeholderTxt = "Add a value that your JSON object may have";
                            break;
                        default:
                            placeholderTxt = "Pick a value to search in";
                    }

                    cy.findAllByText(new RegExp(placeholderTxt, "i")).each(($el, index) => {
                        if (index === 0) {
                            cy.wrap($el)
                                .closest(".react-select__control")
                                .should("not.have.css", "pointer-events", "none")
                                .click({ force: true })
                                .type(inputValue);
                        }
                    });

                    cy.findAllByRole("option").each(($elem) => {
                        const regex = new RegExp(`^${inputValue}`, "i");
                        cy.wrap($elem)
                            .invoke("text")
                            .then((text) => {
                                if (regex.test(text)) {
                                    cy.wrap($elem).click({ force: true });
                                }
                            });
                    });
                });
                // Click the "Add New Parameter" button to add another key-value pair
                cy.findByRole("button", { name: "Add New Parameter" }).click();
            });
        }
    };

    // Handle non-time and non-JSON search cases
    const handleStandardSearch = ($el, field, value) => {
        if (field === "IsNone" || field === "IsNotNone") {
            cy.setInput(field === "IsNone" ? "Is None" : "Is Not None", value === true ? undefined : value);
        } else {
            cy.wrap($el)
                .findByRole("menu")
                .then(($menu) => {
                    if ($menu.find("button").length > 0) {
                        cy.findAllByRole("button", { name: /^Remove / }).each(($elem) => {
                            cy.wrap($elem).click();
                        });
                    }
                });

            const inputType = field === "In" ? "In:" : "Not In:";
            value.forEach((item) => {
                cy.wrap($el).setInput(inputType, item);
            });
        }
    };

    // Main function to handle search params
    const handleSearchParams = (fieldValue, field, value) => {
        openSelect(fieldValue);

        cy.get('[class*="basic-filter dropdown show"]')
            .should("be.visible")
            .within(($el) => {
                if (timeUnits.includes(field)) {
                    // Handle time-related dropdown and interval setting
                    cy.get("#select-range-interval")
                        .type(field)
                        .get(".react-select__menu")
                        .find('div[tabindex="-1"]')
                        .first()
                        .click()
                        .wrap($el)
                        .findByPlaceholderText("Range Value(e.g. 1)")
                        .clear()
                        .type(value);
                } else if ($el.text().includes(jsonPopupTitle)) {
                    // Handle JSON key-value pair search
                    handleJsonSearch(field, value);
                } else {
                    // Handle non-time and non-JSON cases
                    handleStandardSearch($el, field, value);
                }
            });

        openSelect(fieldValue);
    };

    // Iterate over each search parameter and apply the search
    Object.entries(searchParams).forEach(([fieldValue, fieldParams]) => {
        if (Object.entries(fieldParams).length === 1) {
            // Single parameter
            const [field, value] = Object.entries(fieldParams)[0];
            handleSearchParams(fieldValue, field, value);
        } else {
            // Multiple parameters
            Object.entries(fieldParams).forEach(([field, value]) => {
                handleSearchParams(fieldValue, field, value);
            });
        }
    });
};

const createSaveSearches = (searchParams, searchTable = null) => {
    const testrandomString = Math.random().toString(36).substring(7);
    const randomQueryName = `testQueryName${testrandomString}`;

    // If searchTable is provided, set the search table
    if (searchTable) {
        setSearchTable("Search For:", searchTable);
    }

    // Common logic for setting search params
    const fieldKey = Object.keys(searchParams)[0];
    const fieldValue = searchParams[fieldKey];

    // Extract the time unit and its value dynamically
    const timeUnits = ["Seconds", "Minutes", "Hours", "Days", "Weeks", "Months"];
    const timeUnitKey = timeUnits.find((unit) => Object.prototype.hasOwnProperty.call(fieldValue, unit));
    const timeUnitValue = fieldValue[timeUnitKey];

    setSearchParams(searchParams);

    // Click on body to deselect if searchTable is set
    if (searchTable) {
        cy.get("body").click(0, 0);
    }

    cy.findByRole("button", { name: "Save Search" }).click();
    cy.findByText("Query Name:").siblings("#queryname-input").type(randomQueryName);

    // Dynamic query assertion based on the searchParams and time unit
    const expectedQuery = searchTable
        ? `time last ${Object.values(searchParams)[0].Months} months`
        : `time last ${timeUnitValue} ${timeUnitKey.toLowerCase()} and metadata.product.feature.name in sigma_engine,detection_chaining`;

    cy.findByText("Query:").siblings("#query-input").should("have.value", expectedQuery);

    cy.findByRole("button", { name: "Save" }).click();
    cy.contains("Successfully wrote entry to saved searches");

    return searchTable
        ? { selectList: searchTable, queryName: randomQueryName, searchTimeParam: searchParams }
        : { queryName: randomQueryName, searchTimeParam: searchParams };
};

const deleteAllRows = () => {
    // Check for delete button and handle one at a time
    cy.get(".modal-body").then(($modalBody) => {
        const hasDeleteButtons = $modalBody.find("button.btn-danger").length > 0;

        if (!hasDeleteButtons) {
            cy.log("All deletions complete");
            return;
        }

        // Handle single deletion
        cy.get(".modal-body button.btn-danger").first().click();

        // Wait for toast to disappear
        cy.validateToastMessage("Successfully deleted entry from saved searches.");

        // Recurse for next deletion
        deleteAllRows();
    });
};

const deleteSavedSearches = (queryName) => {
    cy.findByText("Saved Searches").click();
    cy.findByText("Filter:").click().type(queryName);
    cy.findByRole("cell", { name: `${queryName}` }).should("be.visible");
    cy.findByRole("button", { name: "Delete" }).click();
    cy.contains("Successfully deleted entry from saved searches");
    cy.findByLabelText("Close").click();
};

const createSearchHistory = (searchTable, searchParams) => {
    setSearchTable("Search For:", searchTable);
    setSearchParams(searchParams);
    cy.get("body").click(0, 0);
    cy.findByRole("button", { name: "Search" }).click();
    return { selectList: searchTable, searchTimeParam: searchParams };
};

const accessSearchHistory = (searchFor, searchParams) => {
    cy.findByText("Search History").click();
    cy.findByText("Filter:").click().type(searchFor);
    cy.get("table").find("tbody").find("tr").contains(searchFor).should("be.visible");
    cy.get("table")
        .find("tbody")
        .find("tr")
        .contains(`time last ${Object.values(searchParams)[0].Months} months`)
        .should("be.visible");
    cy.get('[class*="btn btn-secondary btn-sm"]').contains("Execute").click();
    cy.findByText(searchFor).should("be.visible");
    cy.contains(`${Object.keys(searchParams)[0]} Last ${Object.values(searchParams)[0].Months} month`).should(
        "be.visible"
    );
    cy.findByText("Searching...").should("not.exist");
    cy.findByText("Loading Histogram...").should("not.exist");
    cy.findAllByTitle(`View ${searchFor} Details`).first().click();
    cy.findByText(`${searchFor} Details`).should("be.visible");
    cy.get('[class*="react-json-view"]').find("div").should("be.visible");
    cy.findByLabelText("Close").click();
};

const clickSearch = () => {
    cy.findByRole("button", { name: "Search" }).click();
};

const verifyPageSizeDropdownOptions = () => {
    cy.get('input[id="page-size-select"]')
        .first()
        .click()
        .get(".react-select__menu.css-1nmdiq5-menu")
        .find('div[tabindex*="-1"]')
        .first()
        .click();
};

const formatDate = () => {
    const nthNumber = (number) => {
        if (number > 3 && number < 21) {
            return "th";
        }
        switch (number % 10) {
            case 1:
                return "st";
            case 2:
                return "nd";
            case 3:
                return "rd";
            default:
                return "th";
        }
    };
    const date = new Date();
    const day = date.getDate();
    const month = date.toLocaleDateString("en-us", { month: "short" });
    const year = date.getFullYear();
    return `${month} ${day}${nthNumber(day)} ${year}`;
};

const exportSearchResults = (searchTable, fileType) => {
    cy.findByText("Download")
        .parent()
        .within(($el) => {
            cy.wrap($el).click();
            cy.findByRole("menuitem", { name: fileType.toUpperCase() }).click();
            const filePath = `${searchTable}-${formatDate()}.${fileType}`;
            cy.verifyDownload(filePath, { timeout: 300000, interval: 600 }).should("exist");
        });
};

const addTableColumn = (columnName) => {
    cy.findByText("Customize").click();
    const addColumn = cy.findByLabelText("Add Column").parent();
    addColumn.within(() => {
        cy.get('[id="add-column"]').type(`${columnName}{enter}{enter}`);
    });
    cy.findByRole("button", { name: "Add" }).click();
    cy.findByRole("button", { name: "Update" }).click();
    cy.contains("Column layout updated");
    cy.findByRole("link", { name: "Return to Search" }).click();
};

const removeTableColumn = (columnName) => {
    cy.findByText("Customize").click();
    const tableRow = cy.findByText(columnName).parent();
    tableRow.within(() => {
        cy.findByRole("button", { name: "Remove" }).click();
    });
    cy.findByRole("button", { name: "Update" }).click();
    cy.contains("Column layout updated");
    cy.findByRole("link", { name: "Return to Search" }).click();
};

const setColumnVisibility = (columnName, shouldVisible) => {
    cy.findByText("Customize").click();
    const tableRow = cy.findByText(columnName).parent();
    tableRow.within(() => {
        const checkBox = cy.findByRole("checkbox");
        checkBox.should("be.visible");
        if (shouldVisible === false) {
            checkBox.uncheck();
            checkBox.should("not.be.checked");
        } else {
            checkBox.check();
            checkBox.should("be.checked");
        }
    });
    cy.findByRole("button", { name: "Update" }).click();
    cy.contains("Column layout updated");
    cy.findByRole("link", { name: "Return to Search" }).click();
};

const toggleAllColumnVisibilitiesFromDropdown = () => {
    cy.findByText("Columns").click();
    cy.get('[id="search-results"] thead tr th').then(($initialHeaders) => {
        const initialHeaderTexts = Array.from($initialHeaders).map((el) => el.textContent.trim());

        // Click just one dropdown item instead of all of them
        cy.get(".dropdown-menu.show .dropdown-item")
            .first()
            .then(($btn) => {
                cy.wrap($btn).click();

                // Verify something changed in the headers after clicking
                cy.get('[id="search-results"] thead tr th').should(($newHeaders) => {
                    const newHeaderTexts = Array.from($newHeaders).map((el) => el.textContent.trim());
                    // Check if the headers changed in some way
                    expect(newHeaderTexts.length).not.to.equal(initialHeaderTexts.length);
                });
            });
    });
};

const cancelSearch = () => {
    cy.findByRole("button", { name: "Search" }).click().contains("Cancel").click();
    cy.retryTextNotContainCheck(".alert.alert-info.fade.show", " Canceling search...");
    cy.findByRole("alert")
        .should("be.visible")
        .then(($alert) => {
            // Get the alert text
            const alertText = $alert.text();

            // Verify one of the expected messages is shown
            expect(alertText).to.satisfy(
                (text) =>
                    text.includes("Search cancelled") ||
                    text.includes("Unable to cancel search, it may have already completed.")
            );
        });
};

const getSearchTables = () => {
    cy.findByLabelText("Search For:").click();
    // Capture all search tables
    return cy.get(".react-select__option").then((searchFor) => {
        const searchForOptions = [];
        searchFor.each((index, option) => {
            const trimmedOption = option.innerText.replace(/\s*\(\d+\)/, "").trim();
            searchForOptions.push(trimmedOption);
        });
        return searchForOptions;
    });
};

const removeSelectValues = (label, valuesToRemove) => {
    openSelect(label);

    cy.get('[class*="basic-filter dropdown show"]')
        .should("be.visible")
        .then(($dropdown) => {
            // First check if there are any multi-values present
            const hasMultiValues = $dropdown.find(".react-select__multi-value__label").length > 0;

            if (hasMultiValues) {
                cy.wrap($dropdown)
                    .find(".react-select__multi-value__label")
                    .each(($label) => {
                        const currentValue = $label.text();
                        if (valuesToRemove.includes(currentValue)) {
                            // Use then() to handle cases where remove button might not exist
                            cy.wrap($dropdown)
                                .find(`[aria-label="Remove ${currentValue}"]`)
                                .then(($btn) => {
                                    if ($btn.length) {
                                        cy.wrap($btn).click({ force: true });
                                    }
                                });
                        }
                    });
            }
        });

    openSelect(label); // Close the dropdown
};

const setPageSize = (pageSize) => {
    cy.get("input[id='page-size-select']").first().type(`Show ${pageSize} results per page`);
    cy.get(".react-select__menu.css-1nmdiq5-menu").find('div[tabindex*="-1"]').first().click();
};

export {
    accessSearchHistory,
    addTableColumn,
    cancelSearch,
    clickSearch,
    createSaveSearches,
    createSearchHistory,
    deleteAllRows,
    deleteSavedSearches,
    exportSearchResults,
    getSearchTables,
    removeSelectValues,
    removeTableColumn,
    setColumnVisibility,
    setPageSize,
    setSearchParams,
    setSearchTable,
    toggleAllColumnVisibilitiesFromDropdown,
    verifyPageSizeDropdownOptions,
};
