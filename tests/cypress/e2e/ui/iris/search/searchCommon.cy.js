/// <reference types="cypress"/>

import { clickSearch, deleteAllRows, setSearchParams, setSearchTable } from "./search_utils";

describe("Tenant: Common components of Search", { tags: ["full", "search", "data_faker"] }, () => {
    const searchParams = {
        "Modified Time:": { Months: 12 },
    };
    const searchTable = "Device";
    const testrandomString = Math.random().toString(36).substring(7);

    beforeEach(() => {
        cy.loginSession(
            Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
            Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
        );
        cy.navigateTopNav("Search");
        cy.retryTextNotContainCheck(".alert.alert-info.fade.show", " Loading tables...");
    });

    afterEach(() => {
        cy.findByText("Customize").click();
        cy.findByRole("button", { name: "Reset to Default" }).click();
        cy.contains("Column layout reset to default");
    });

    after(() => {
        cy.clickLogout();
    });

    it("Admin user can create a saved searches and access the saved details", { tags: "regression" }, () => {
        const savedSearchTable = "Authentication";
        const savedSearchParams = {
            "Event Time:": { Months: 2 },
        };
        const randomQueryName = `testQueryName${testrandomString}`;
        const searchQuery = "time last 2 months";

        // Remove previously saved search
        cy.findByText("Saved Searches").click();
        cy.get('[role="dialog"] table')
            .find("tbody")
            .invoke("text")
            .then((txt) => {
                if (!txt.includes("No Saved Searches")) {
                    deleteAllRows();
                    cy.findByLabelText("Close").click();
                } else {
                    cy.findByLabelText("Close").click();
                }
            });

        // Create saved search
        setSearchTable("Search For:", savedSearchTable);
        setSearchParams(savedSearchParams);
        cy.findByRole("button", { name: "Save Search" }).click();
        cy.findByText("Query Name:").siblings("#queryname-input").type(randomQueryName);
        cy.findByText("Query:").siblings("#query-input").should("have.value", searchQuery);
        cy.findByRole("button", { name: "Save" }).click();
        cy.contains("Successfully wrote entry to saved searches");

        // Access saved search and excute it
        cy.findByText("Saved Searches").click();
        cy.findByText("Filter:").click().type(randomQueryName);
        cy.findByRole("cell", { name: `${randomQueryName}` }).should("be.visible");
        cy.findByText(searchQuery).should("be.visible");
        cy.findByRole("button", { name: "Execute" }).click();
        cy.findByText(savedSearchTable).should("be.visible");
        cy.contains(
            `${Object.keys(savedSearchParams)[0]} Last ${Object.values(savedSearchParams)[0].Months} month`
        ).should("be.visible");
        cy.findByRole("button", { name: "Search" }).click();
        cy.findAllByTitle(`View ${savedSearchTable} Details`).first().click();
        cy.findByText(`${savedSearchTable} Details`).should("be.visible");
        cy.get('[class="modal-body"]').find("div").should("be.visible");
        cy.findByLabelText("Close").click();

        // Delete saved search
        cy.findByText("Saved Searches").click();
        cy.findByText("Filter:").click().type(randomQueryName);
        cy.findByRole("cell", { name: `${randomQueryName}` }).should("be.visible");
        cy.findByRole("button", { name: "Delete" }).click();
        cy.contains("Successfully deleted entry from saved searches");
        cy.findByLabelText("Close").click();
    });

    it("Admin user can create a save as default query", { tags: "regression" }, () => {
        setSearchTable("Search For:", searchTable);
        setSearchParams(searchParams);
        clickSearch();

        cy.findByRole("button", { name: "Save as default" }).click();
        cy.checkElementIsVisible(".modal.fade.show", 20);
        cy.findByRole("dialog", { timeout: 60000 })
            .should("be.visible")
            .within(() => {
                cy.findByRole("heading", { name: "Save as default query" }).should("be.visible");
                cy.findByRole("button", { name: "Yes" }).should("be.visible").click();
            });

        cy.validateToastMessage("Search configuration updated.");
    });

    it("Admin user can create a search history and access the search history", { tags: "regression" }, () => {
        const searchHistoryTable = "User";
        const searchHistoryParams = {
            "Modified Time:": { Months: 2 },
        };
        const searchHistoryQuery = "time last 2 months";

        setSearchTable("Search For:", searchHistoryTable);
        setSearchParams(searchHistoryParams);
        clickSearch();

        // Access search history
        cy.findByText("Search History").click();
        cy.findByText("Filter:").click().type(searchHistoryTable);

        cy.get("table.rt-table").contains("td", searchHistoryTable).parent("tr").should("be.visible");
        cy.get("table.rt-table").contains("td", searchHistoryQuery).parent("tr").should("be.visible");
        cy.get("table.rt-table").find("tbody").find("tr").contains("Execute").click();
        cy.findByText(searchHistoryTable).should("be.visible");
        cy.contains(
            `${Object.keys(searchHistoryParams)[0]} Last ${Object.values(searchHistoryParams)[0].Months} month`
        ).should("be.visible");
        cy.findByText("Searching...").should("not.exist");
        cy.findByText("Loading Histogram...").should("not.exist");
        cy.findAllByTitle(`View ${searchHistoryTable} Details`).first().click();
        cy.findByText(`${searchHistoryTable} Details`).should("be.visible");
        cy.get('[class*="react-json-view"]').find("div").should("be.visible");
        cy.findByLabelText("Close").click();
    });
});
