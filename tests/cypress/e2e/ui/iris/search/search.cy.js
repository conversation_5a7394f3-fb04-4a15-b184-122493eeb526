/// <reference types="cypress"/>

import {
    addTableColumn,
    cancelSearch,
    clickSearch,
    exportSearchResults,
    removeTableColumn,
    setColumnVisibility,
    setSearchParams,
    setSearchTable,
    toggleAllColumnVisibilitiesFromDropdown,
    verifyPageSizeDropdownOptions,
} from "./search_utils";

describe("Tenant: Search", { tags: ["full", "search", "data_faker"] }, () => {
    const searchParams = {
        "Modified Time:": { Months: 12 },
    };

    const searchTable = "User";
    beforeEach(() => {
        cy.loginSession(
            Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
            Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
        );
    });

    after(() => {
        cy.navigateTopNav("Search");
        cy.retryTextNotContainCheck(".alert.alert-info.fade.show", " Loading tables...");
        setSearchTable("Search For:", searchTable);
        setSearchParams(searchParams);
        clickSearch();
        cy.findByText("Loading Histogram...").should("not.exist");
        cy.findByText("Searching...").should("not.exist");
        cy.findByText("Customize").click();
        cy.findByRole("button", { name: "Reset to Default" }).click();
        cy.contains("Column layout reset to default");
        cy.clickLogout();
    });
    const randomString = Math.random().toString(36).substring(8);
    const titleName = `Chain-${randomString}`;

    it("Admin user can access the page size, customize and download dropdown options.", { tags: "regression" }, () => {
        cy.navigateTopNav("Search");
        cy.retryTextNotContainCheck(".alert.alert-info.fade.show", " Loading tables...");
        setSearchTable("Search For:", searchTable);
        setSearchParams(searchParams);
        clickSearch();
        verifyPageSizeDropdownOptions();
        exportSearchResults(searchTable, "json");
        exportSearchResults(searchTable, "csv");
        const columnName = "Full Name";
        // set column should not visibile
        setColumnVisibility(columnName, false);
        cy.findByText(searchTable).should("be.visible");
        setSearchParams(searchParams);
        clickSearch();
        // verify column should not visibile
        cy.findAllByText(columnName).should("not.exist");
        // set column should be visibile
        setColumnVisibility(columnName, true);
        setSearchParams(searchParams);
        clickSearch();
        // verify column should be visibile
        cy.findAllByText(columnName).should("exist");
    });

    it(
        "Admin user can add and remove the customize columnfield and verify the columns dropdown options",
        { tags: "regression" },
        () => {
            cy.navigateTopNav("Search");
            cy.retryTextNotContainCheck(".alert.alert-info.fade.show", " Loading tables...");
            const table = "Device";
            const columnName = "Organization";
            setSearchTable("Search For:", table);
            cy.findByText("Loading Histogram...").should("not.exist");
            cy.findByText("Searching...").should("not.exist");
            setSearchParams(searchParams);
            clickSearch();
            cy.findByText("Loading Histogram...").should("not.exist");
            cy.findByText("Searching...").should("not.exist");
            // add customize column by pass true
            addTableColumn(columnName);
            cy.retryTextNotContainCheck(".alert.alert-info.fade.show", " Loading tables...");
            // verfy searchTable should be visible
            cy.get(".react-select__single-value").contains(table).should("be.visible");
            // cy.findByText(searchTable).should("be.visible");
            // perform click search followed by setSearchParams to verify customize column
            setSearchParams(searchParams);
            clickSearch();
            cy.findByText("Loading Histogram...").should("not.exist");
            cy.findByText("Searching...").should("not.exist");
            // verify added customize column should be visiable
            cy.findAllByText(columnName).should("exist");

            // remove customize column
            removeTableColumn(columnName);

            setSearchParams(searchParams);
            clickSearch();
            cy.findByText("Loading Histogram...").should("not.exist");
            cy.findByText("Searching...").should("not.exist");
            // verify removed customize column should be not visiable
            cy.findAllByText(columnName).should("not.exist");

            toggleAllColumnVisibilitiesFromDropdown();
        }
    );

    it("Admin users can cancel the search option", { tags: "smoke" }, () => {
        cy.navigateTopNav("Search");
        cy.retryTextNotContainCheck(".alert.alert-info.fade.show", " Loading tables...");
        setSearchTable("Search For:", searchTable);
        setSearchParams(searchParams);
        cancelSearch();
    });

    it("Admin can create new detection chains", { tags: "regression" }, () => {
        cy.navigateTopNav("Search");
        setSearchTable("Search For:", "Process Activity");
        cy.retryTextNotContainCheck(".alert.alert-info.fade.show", " Loading tables...");
        cy.findByRole("button", { name: "Create Detection Chain" }).click();
        cy.findByText("Create Detection Chain").should("be.visible");
        cy.findAllByRole("button", { name: /save/i }).should("be.disabled");
        const validChainForm = {
            "Title *": titleName,
            "Status *": "Test",
            "Severity *": "High",
            Description: "To view the information chain links",
            Tags: "Detect_Chain",
            "Run Frequency": 12,
            "Create Passparameter:": "cloud.region",
            "Parameter Name:": "us-east-1",
        };
        cy.updateForm(validChainForm);
        cy.findByText("No Links added to this Detection Chain yet.").should("be.visible");
        setSearchTable("Create Link For:", "Process Activity");
        cy.findByRole("button", { name: "Add Link" }).click();
        cy.findByText("Save Chain").click();
        cy.findByText("Detection Chain created").should("be.visible");
        cy.navigateTopNav("Security", "Detection Chains");
        const filterParams = {
            "Title:": { In: [titleName] },
        };
        setSearchParams(filterParams);
        cy.findByRole("button", { name: "Apply" }).click();
        cy.clickViewDetails("Actions", "Delete Detection Chain", "title");
        cy.checkElementIsVisible(".modal.fade.show", 30);
        cy.findByRole("dialog", { timeout: 60000 })
            .should("be.visible")
            .within(() => {
                // Look for the heading inside the modal
                cy.findByRole("heading", { name: "Confirm Delete" }).should("be.visible");

                // Find and click the Yes button
                cy.findByRole("button", { name: "Yes" }).should("be.visible").click();
            });
        cy.findByText("Detection Chain deleted").should("be.visible");
    });
});
