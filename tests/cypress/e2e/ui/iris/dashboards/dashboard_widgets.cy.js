/// <reference types="cypress"/>
import {
    addCountTrend,
    addFieldGraph,
    addWidget,
    deleteWidget,
    editWidget,
    processPivotTableWidgetAndVerifyAllLinks,
    resetToDefault,
    validateWidgetContentDisplay,
} from "./dashboard_utils";

describe("Tenant: Console Widgets", { tags: ["full", "dashboard_widgets"] }, () => {
    beforeEach(() => {
        cy.loginSession(
            Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
            Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
        );
    });

    after(() => {
        cy.clickLogout();
    });

    it("Admin user can add, edit & delete Count Trend widget to Overview dashboard", { tags: "regression" }, () => {
        cy.retryTextNotContainCheck(".alert.alert-info.fade.show", "Loading...");
        const randomWidgetName = `Count Trend - ${Math.random().toString(36).substring(8)}`;
        addCountTrend("Count Trend", randomWidgetName, "Active Devices Observed", "Past 1 Day");
        cy.retryTextNotContainCheck(".alert.alert-info.fade.show", "Loading data...");
        cy.findByText(`${randomWidgetName} (Past 1 Day)`, { exact: false }).should("be.visible");
        editWidget("Count Trend", randomWidgetName, "Time Range", "Past 10 Seconds");
        cy.retryTextNotContainCheck(".alert.alert-info.fade.show", "Loading data...");
        cy.findByText(`${randomWidgetName} (Past 10 Seconds)`, { exact: false }).should("be.visible");
        deleteWidget(randomWidgetName);
        cy.retryTextNotContainCheck(".alert.alert-info.fade.show", "Loading data...");
        cy.get(".dashboard-widget.card").each(($el) => {
            cy.wrap($el).invoke("text").should("not.equal", randomWidgetName);
        });
    });

    it("Admin user can add, edit & delete Header widget to Overview dashboard", { tags: "regression" }, () => {
        cy.retryTextNotContainCheck(".alert.alert-info.fade.show", "Loading...");
        const randomWidgetName = `Header - ${Math.random().toString(36).substring(8).toUpperCase()}`;
        addWidget("Header", randomWidgetName);
        cy.retryTextNotContainCheck(".alert.alert-info.fade.show", "Loading data...");
        const updatedWidgetName = `${randomWidgetName} - ${Math.random().toString(36).substring(8).toUpperCase()}`;
        editWidget("Header", randomWidgetName, "Widget Name*", updatedWidgetName);
        cy.retryTextNotContainCheck(".alert.alert-info.fade.show", "Loading data...");
        cy.findByText(updatedWidgetName.toUpperCase(), { exact: false }).should("be.visible");
        deleteWidget(updatedWidgetName);
        cy.get(".dashboard-widget.card").each(($el) => {
            cy.wrap($el).invoke("text").should("not.equal", updatedWidgetName);
        });
    });

    it("Admin can view Security Hygiene Widgets and Navigate Pivot Tables", { tags: ["demo_staging"] }, () => {
        cy.navigateTopNav("Console", "Overview");
        cy.retryTextNotContainCheck(".alert.alert-primary.fade.show", "Loading...");
        cy.findByText("Security Data Fabric - Health", { selector: ".dashboard-widget.card h5" }).should("be.visible");
        validateWidgetContentDisplay("Storage Usage");
    });

    it("Admin can view Security Threats Widgets and Navigate Pivot Tables", { tags: ["demo_staging"] }, () => {
        cy.navigateTopNav("Console", "Overview");
        cy.retryTextNotContainCheck(".alert.alert-primary.fade.show", "Loading...");
        cy.findByText("Security Threats", { selector: ".dashboard-widget.card h5" }).should("be.visible");
        validateWidgetContentDisplay("Highest Detection Findings From DataBee");
        validateWidgetContentDisplay("All Findings by Entity Type");
        processPivotTableWidgetAndVerifyAllLinks("Most Findings By User");
        processPivotTableWidgetAndVerifyAllLinks("Highest Severity Findings From DataBee");
    });

    Object.entries(Cypress.env("credentials").DASHBOARD_RESET.USERS).forEach(([UserName, UserId]) => {
        it(
            `${UserName} user can add widgets to Overview dashboard, reset to default, verify widget deleted`,
            { tags: "regression" },
            () => {
                cy.visit(Cypress.env("credentials").DASHBOARD_RESET.URL);
                cy.manualLogin(UserId, Cypress.env("credentials").DASHBOARD_RESET.PASS[UserName]);
                cy.navigateTopNav("Console", "Overview");
                cy.retryTextNotContainCheck(".alert.alert-info.fade.show", "Loading...");
                const randomText = Math.random().toString(36).substring(2, 6);
                const widgetName = `Dashboard Reset Widget - ${randomText}`;
                addFieldGraph("Field Graph", widgetName, "Authentication");
                cy.findByRole("dialog").should("not.exist");
                cy.findByText(widgetName, { exact: false }, { selector: ".dashboard-widget.card h5" }).should(
                    "be.visible"
                );
                resetToDefault();
                cy.findByText(widgetName, { exact: false }, { selector: ".dashboard-widget.card h5" }).should(
                    "not.exist"
                );
            }
        );
    });
});
