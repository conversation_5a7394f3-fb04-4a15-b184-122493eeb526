/// <reference types="cypress"/>
import { createDashboard, deleteDashboard, verifyDashboard } from "./dashboard_utils";

const randomString = Math.random().toString(36).substring(8);
const randomNumber = Math.floor(Math.random() * (1000 - 100 + 1)) + 100;
const newDashboardName = `MyTestDashboard${randomString}-${randomNumber}`;

describe("Tenant: Console: Un-authorized user ", { tags: ["full", "dashboard"] }, () => {
    beforeEach(() => {
        cy.clearCookies();
        cy.visit(Cypress.env("credentials").DATABEE_UI);
    });

    it("Analyst user cannot access dashboard created by databee admin", { tags: "regression" }, () => {
        cy.manualLogin(
            Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
            Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
        );
        cy.navigateTopNav("Console", "Overview");
        createDashboard(newDashboardName, false);
        verifyDashboard(newDashboardName);
        cy.findByTitle("My Profile")
            .parent()
            .within(($el) => {
                cy.wrap($el).click();
                cy.findByText("Logout").click();
            });
        cy.manualLogin(
            Cypress.env("credentials").DATABEE_USER_IDS.ANALYST,
            Cypress.env("credentials").DATABEE_ANALYST_PASSWORD
        );
        cy.navigateTopNav("Console", "Overview");
        cy.findByRole("button", { name: newDashboardName }).should("not.exist");
        cy.visit(`${Cypress.env("credentials").DATABEE_UI}console/${newDashboardName}`);
        cy.get("div.container-fluid").should("be.visible");
        cy.findByText("Page Not Found").should("be.visible");
        cy.findByRole("link", { name: "Return to Console" }).should("be.visible");
        cy.findByTitle("My Profile")
            .parent()
            .within(($el) => {
                cy.wrap($el).click();
                cy.findByText("Logout").click();
            });
        cy.manualLogin(
            Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
            Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
        );
        deleteDashboard(newDashboardName);
        cy.findByTitle("My Profile")
            .parent()
            .within(($el) => {
                cy.wrap($el).click();
                cy.findByText("Logout").click();
            });
    });
});
