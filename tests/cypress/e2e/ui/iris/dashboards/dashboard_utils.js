/// <reference types="cypress"/>

/**
 * Utility functions for interacting with dashboards and widgets.
 * Provides methods to create, edit, delete, and verify dashboards and widgets, as well as validate their content and navigation.
 */

/**
 * Creates a new dashboard with a unique name.
 * Optionally sets it as the default dashboard.
 *
 * @param {string} dashboardName - The name for the new dashboard.
 * @param {boolean} setDefault - Whether to set the dashboard as default.
 */
const createDashboard = (dashboardName, setDefault) => {
    cy.findByText("Manage Console").click();
    cy.findByText("Create Console").click();
    cy.findByText("Create").should("be.disabled");
    cy.findByLabelText("Name*").type(dashboardName);
    if (setDefault) {
        cy.findByLabelText("Make Default").check().should("be.checked");
    } else {
        cy.findByLabelText("Make Default").should("not.be.checked");
    }
    cy.findByText("Create").click();
};

/**
 * Verifies that a dashboard with the given name is visible in the dashboard menu.
 * @param {string} dashboardName - The name of the dashboard to verify.
 */
const verifyDashboardExistsInNavBar = (dashboardName) => {
    cy.findByRole("link", { name: "Console" }).then(($el) => {
        if ($el.hasClass("dropdown-toggle")) {
            cy.wrap($el).click();
            cy.findByRole("menuitem", { name: dashboardName }).should("be.visible");
        }
    });
};

/**
 * Edits the name of the current dashboard.
 * @param {string} dashboardName - The new name for the dashboard.
 */
const updateCurrentDashboardName = (dashboardName) => {
    cy.findByText("Manage Console").click();
    cy.findByText("Edit Console").click();
    cy.findByText("Edit").should("be.enabled");
    cy.findByLabelText("Name*").clear().type(dashboardName);
    cy.findByText("Edit").click();
};

/**
 * Deletes the specified dashboard.
 * @param {string} dashboardName - The name of the dashboard to delete.
 */
const deleteDashboard = (dashboardName) => {
    cy.navigateTopNav("Console", dashboardName);
    cy.findByRole("button", { name: "Manage Console" }).click({ force: true });
    cy.findByText("Delete Console").click({ force: true });
    cy.findByRole("dialog")
        .find(".form-horizontal")
        .within(() => {
            cy.findByText("Delete Console").should("be.visible");
            cy.findByRole("button", { name: "Delete" }).click({ force: true });
        });
    cy.findByText("Console").click();
    cy.findAllByText(dashboardName).should("not.exist");
};

/**
 * Restores the default console dashboard, removing all custom widgets and resetting to default state.
 */
const restoreDefaultConsole = () => {
    cy.findByText("Manage Console")
        .click()
        .next("div")
        .within(() => {
            cy.findByText("Restore Default Console").click();
        });
    cy.findByRole("dialog").within(() => {
        cy.get(".modal-title").should("contain.text", "Reset Console");
        cy.findByRole("button", { name: "Reset" }).should("be.enabled").click();
    });
    cy.findByRole("dialog").should("not.exist");
};

// Methods to interact with widgets

/**
 * Selects a widget type to add to the dashboard.
 * @param {string} widgetType - The type of widget to select.
 */
const selectWidget = (widgetType) => {
    cy.findByText("Add Widget").should("be.enabled").click();
    cy.findAllByText("Add Widget").last().should("be.visible");
    cy.retryTextNotContainCheck(".alert.alert-info.fade.show", "Loading...");
    cy.setInput("Widget Type*", widgetType);
    cy.findByRole("button", { name: "Next" }).click();
    cy.findByText(`Add Widget - ${widgetType}`).should("be.visible");
};

/**
 * Adds a Count Trend widget to the dashboard.
 *
 * @param {string} widgetName - The name for the widget.
 * @param {string} counterType - The counter type for the widget.
 * @param {string|number} timeRange - The time range for the widget.
 */
const addCountTrendWidget = (widgetName, counterType, timeRange) => {
    selectWidget("Count Trend");
    cy.setInput("Widget Name*", widgetName);
    cy.setInput("Counter Type", counterType);
    cy.get("#timerange").clear().type(`${timeRange}{enter}`);
    cy.findByRole("button", { name: "Save" }).click();
    cy.findByRole("dialog").should("not.exist");
};

/**
 * Adds a Field Graph widget to the dashboard.
 *
 * @param {string} widgetName - The name for the widget.
 * @param {string} widgetTableType - The table type for the widget.
 */
const addFieldGraphWidget = (widgetName, widgetTableType) => {
    selectWidget("Field Graph");
    cy.setInput("Widget Name*", widgetName);
    cy.setInput("Table*", widgetTableType);
    cy.findAllByRole("button", { name: "Close" }).last().should("be.visible");
    cy.findByRole("button", { name: "Save" }).click({ force: true });
    cy.findByRole("dialog").should("not.exist");
};

/**
 * Adds a Field Value widget to the dashboard.
 *
 * @param {string} widgetType - The type of graph widget.
 * @param {string} widgetName - The name for the widget.
 * @param {string} activity - The activity for the widget.
 * @param {string} fieldName - The field name for the widget.
 */
const addFieldValueWidget = (widgetType, widgetName, activity, fieldName) => {
    selectWidget(widgetType);
    cy.setInput("Widget Name*", widgetName);
    cy.setInput("Activity*", activity);
    cy.setInput("Field Name*", fieldName);
    cy.findByRole("button", { name: "Back" }).should("be.visible");
    cy.findAllByRole("button", { name: "Close" }).last().should("be.visible");
    cy.findByRole("button", { name: "Save" }).click({ force: true });
    cy.findByRole("dialog").should("not.exist");
};

/**
 * Adds a Finding Threat Overview widget to the dashboard.
 *
 * @param {string} widgetName - The name for the widget.
 * @param {string} widgetTableType - The findings table type for the widget.
 */
const addFindingThreatOverviewWidget = (widgetName, widgetTableType) => {
    selectWidget("Finding Threat Overview");
    cy.findByLabelText("Widget Name*").type(widgetName);
    cy.setInput("Findings Table*", widgetTableType);
    cy.findByRole("button", { name: "Back" }).should("be.visible");
    cy.findAllByRole("button", { name: "Close" }).last().should("be.visible");
    cy.findByRole("button", { name: "Save" }).click({ force: true });
    cy.findByRole("dialog").should("not.exist");
};

/**
 * Adds a Statistics widget to the dashboard.
 *
 * @param {string} widgetName - The name for the widget.
 * @param {string} widgetTableType - The table type for the widget.
 */
const addStatisticsWidget = (widgetName, widgetTableType) => {
    selectWidget("Statistics");
    cy.findByLabelText("Widget Name*").type(widgetName);
    cy.findByRole("dialog").within(() => {
        cy.findAllByRole("combobox").eq(0).click();
    });
    cy.findByText(widgetTableType).click();
    cy.findByRole("button", { name: "Back" }).should("be.visible");
    cy.findAllByRole("button", { name: "Close" }).last().should("be.visible");
    cy.findByRole("button", { name: "Save" }).click();
    cy.findByRole("dialog").should("not.exist");
};

/**
 * Adds a widget to the dashboard, that just need a name to be create.
 * @param {string} widgetType - The type of graph widget.
 * @param {string} widgetName - The new name for the dashboard.
 */
const addWidget = (widgetType, widgetName) => {
    selectWidget(widgetType);
    cy.setInput("Widget Name*", widgetName);
    cy.findByRole("button", { name: "Back" }).should("be.visible");
    cy.findAllByRole("button", { name: "Close" }).last().should("be.visible");
    cy.findByRole("button", { name: "Save" }).click({ force: true });
    cy.findByRole("dialog").should("not.exist");
};

/**
 * Edits the name value of any widget.
 *
 * @param {string} widgetType - The type of widget.
 * @param {string} widgetName - The name of the widget to edit.
 * @param {string} fieldLabel - The label of the field to edit.
 * @param {string} newValue - The new value to set.
 */
const editWidget = (widgetType, widgetName, fieldLabel, newValue) => {
    cy.findByText(widgetName, { exact: false })
        .parents("div.dashboard-widget.card")
        .within(() => {
            cy.findByText(widgetName, { exact: false }).should("be.visible");
            cy.findByTitle("Edit").click();
        });
    cy.findByText("Loading...").should("not.exist");
    cy.findByRole("dialog")
        .should("be.visible")
        .within(() => {
            cy.findByText(`Update Widget - ${widgetType}`, { exact: false }).should("be.visible");
            cy.setInput(fieldLabel, newValue);
            cy.findByRole("button", { name: "Save" }).click();
        });
    cy.findByRole("dialog").should("not.exist");
};

/**
 * Deletes a widget from the dashboard.
 *
 * @param {string} widgetName - The name of the widget to delete.
 */
const deleteWidget = (widgetName) => {
    cy.log(`Print widget name: ${widgetName}`);
    cy.contains("div", widgetName, { exact: false })
        .parents("div.dashboard-widget.card")
        .within(() => {
            cy.findByTitle("Delete").click();
        });
    cy.findByRole("dialog")
        .should("be.visible")
        .within(() => {
            cy.findByText("Confirm Delete", { exact: false }).should("be.visible");
            cy.findByText("Are you sure you want to delete this widget?").should("be.visible");
            cy.findByRole("button", { name: "Yes" }).click({ force: true });
        });
    cy.findByRole("dialog").should("not.exist");
    cy.findAllByText(widgetName).should("not.exist");
};

const checkDataSourceState = (sourceName, state) => {
    cy.get("[title='Data Feed']")
        .contains(sourceName)
        .then(($element) => {
            const $statusIndicator = $element.parentsUntil("#data-source-list").find(".status-indicator-state").first();
            cy.wrap($statusIndicator).should("have.text", state);
        });
};

/**
 * Clicks a link in a pivot table widget and verifies navigation.
 *
 * @param {string} widgetTitle - The title of the widget.
 * @param {string} linkText - The text of the link to click.
 */
const pivotTableWidgetAndVerifyNavigation = (widgetTitle, linkText) => {
    cy.findByText(widgetTitle, { exact: false })
        .should("exist")
        .and("be.visible")
        .closest(".dashboard-widget")
        .within(() => {
            cy.get(".card-body").within(() => {
                cy.get("table").contains("a", linkText).click();
            });
        });
};

/**
 * Processes all links in a pivot table widget, verifies navigation, and checks for errors.
 * Throws an error if any link navigation fails.
 *
 * @param {string} widgetTitle - The title of the widget.
 */
const processPivotTableWidgetAndVerifyAllLinks = (widgetTitle) => {
    const failedOptions = [];

    cy.findByText(widgetTitle, { exact: false })
        .should("exist")
        .and("be.visible")
        .closest(".dashboard-widget")
        .within(() => {
            cy.get(".card-body").then(($body) => {
                if ($body.find('.alert[role="alert"]:contains("No data available.")').length > 0) {
                    cy.findByText("No data available.").should("be.visible");
                    cy.wrap(null).as("totalLinks");
                } else {
                    // Just count the links, don't store texts yet
                    cy.get("table").should("be.visible").find("a").its("length").as("totalLinks");
                }
            });
        });

    // Get the link count
    cy.get("@totalLinks").then((totalLinks) => {
        // If no links, exit early
        if (totalLinks <= 0) {
            cy.log(`No links to process in widget "${widgetTitle}"`);
            return;
        }

        // Process each link individually
        for (let i = 0; i < totalLinks; i += 1) {
            cy.log(`Processing link ${i + 1}/${totalLinks}`);

            // Get the text of the current link before clicking
            cy.findByText(widgetTitle, { exact: false })
                .closest(".dashboard-widget")
                .within(() => {
                    cy.get("table")
                        .find("a")
                        .eq(i)
                        .invoke("text")
                        .then((link) => {
                            const linkText = link.trim();
                            cy.wrap(linkText).as("linkText");
                            cy.log(`Clicking link with text: "${linkText}"`);

                            // Click the same link we just read
                            cy.get("table").find("a").eq(i).click();
                        });
                });

            // Wait for loading to finish
            cy.retryTextNotContainCheck(".alert.alert-primary.fade.show", "Loading...");

            // Check for errors
            cy.get("body").then(($body) => {
                const alert = $body.find('[role="alert"]');

                if (alert.length > 0 && alert.hasClass("alert-danger")) {
                    // Get page title or URL to identify the page with error
                    cy.url().then((url) => {
                        failedOptions.push(`${widgetTitle} - Page at ${url}`);
                    });
                } else {
                    // Verify contents based on widget type
                    cy.get("@linkText").then((linkText) => {
                        if (widgetTitle === "Highest Severity Findings From DataBee") {
                            cy.retryTextNotContainCheck(".alert", "Loading DataBee Findings...");
                            cy.findAllByText("DataBee Findings").should("exist");
                            cy.get("canvas[role='img']").should("exist");
                            cy.get("#search-results").should("exist");
                        } else {
                            cy.get("h1.card-title").invoke("text").should("include", linkText.trim());
                        }
                    });
                }
            });

            // Navigate back to console
            cy.navigateTopNav("Console", "Overview");
        }
    });

    // Fail the test if any options caused errors
    cy.then(() => {
        if (failedOptions.length > 0) {
            throw new Error(`The following options caused errors: ${failedOptions.join(", ")}`);
        }
    });
};

/**
 * Validates that a widget displays its content correctly.
 * Checks for "No data available" alerts or verifies chart presence.
 *
 * @param {string} widgetTitle - The title of the widget.
 */
const validateAllWidgetsDisplayWithContent = () => {
    cy.get(".dashboard-widget").each(($dashboard) => {
        cy.wrap($dashboard)
            .find(".card-body")
            .should("exist")
            .find('.alert[role="alert"]:contains("No data available")')
            .should("not.exist");
    });
};

export {
    addCountTrendWidget,
    addFieldGraphWidget,
    addFieldValueWidget,
    addFindingThreatOverviewWidget,
    addStatisticsWidget,
    addWidget,
    checkDataSourceState,
    createDashboard,
    deleteDashboard,
    deleteWidget,
    editWidget,
    pivotTableWidgetAndVerifyNavigation,
    processPivotTableWidgetAndVerifyAllLinks,
    restoreDefaultConsole,
    updateCurrentDashboardName,
    validateAllWidgetsDisplayWithContent,
    verifyDashboardExistsInNavBar,
};
