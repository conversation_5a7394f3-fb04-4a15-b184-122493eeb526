/// <reference types="cypress"/>

// Methods to interact with dashboard/console
const createDashboard = (dashboardName, setDefault) => {
    cy.findByText("Manage Console").click();
    cy.findByText("Create Console").click();
    cy.findByText("Create").should("be.disabled");
    cy.findByLabelText("Name*").type(dashboardName);
    // Dashboard default selection
    if (setDefault) {
        cy.findByLabelText("Make Default").check().should("be.checked");
    } else {
        cy.findByLabelText("Make Default").should("not.be.checked");
    }
    cy.findByText("Create").click();
};

const verifyDashboard = (dashboardName) => {
    cy.findByRole("link", { name: "Console" }).then(($el) => {
        if ($el.hasClass("dropdown-toggle")) {
            cy.wrap($el).click();
            cy.findByRole("menuitem", { name: dashboardName }).should("be.visible");
        }
    });
};

const editDashboardName = (dashboardName) => {
    cy.findByText("Manage Console").click();
    cy.findByText("Edit Console").click();
    cy.findByText("Edit").should("be.enabled");
    cy.findByLabelText("Name*").clear().type(dashboardName);
    cy.findByText("Edit").click();
};

const verifyOverviewConsoleWidgets = () => {
    cy.findByText("Security Data Fabric - Health").should("be.visible");
    cy.findByText("Security Hygiene").should("be.visible");
    cy.findByText("Security Threats").should("be.visible");
    cy.findAllByRole("alert").should("not.exist");
};

const deleteDashboard = (DashboardName) => {
    cy.navigateTopNav("Console", DashboardName);
    cy.findByRole("button", { name: "Manage Console" }).click({ force: true });
    cy.findByText("Delete Console").click({ force: true });
    cy.findByRole("dialog")
        .find(".form-horizontal")
        .within(() => {
            cy.findByText("Delete Console").should("be.visible");
            cy.findByRole("button", { name: "Delete" }).click({ force: true });
        });
    cy.findByText("Console").click();
    cy.findAllByText(DashboardName).should("not.exist");
};

const restoreDefaultConsole = () => {
    cy.findByRole("link", { name: "Console" }).then(($el) => {
        if ($el.hasClass("dropdown-toggle")) {
            cy.wrap($el).click();
            cy.findAllByRole("menuitem").each(($item) => {
                cy.wrap($item).then(($elem) => {
                    cy.wrap($elem).click({ force: true });
                    cy.findByRole("button", { name: "Manage Console" }).click();
                    cy.findByText("Delete Console").click();
                    cy.findByRole("dialog")
                        .find(".form-horizontal")
                        .within(() => {
                            cy.findByText("Delete Console").should("be.visible");
                            cy.findByRole("button", { name: "Delete" }).click();
                        });
                });
            });
            cy.findByRole("button", { name: "Manage Console" }).click();
            cy.findByText("Restore Default Console").click({ force: true });
            cy.findByRole("dialog")
                .find(".form-horizontal")
                .within(() => {
                    cy.findByText("Reset Console").should("be.visible");
                    cy.findByRole("button", { name: "Reset" }).click();
                });
        } else {
            cy.wrap($el).click();
        }
    });
};

// Methods to interact with widgets
const selectWidget = (graphType) => {
    cy.findByText("Add Widget").should("be.enabled").click();
    cy.findAllByText("Add Widget").last().should("be.visible");
    cy.retryTextNotContainCheck(".alert.alert-info.fade.show", "Loading...");
    cy.setInput("Widget Type*", graphType);
    cy.findByRole("button", { name: "Next" }).click();
    cy.findByText(`Add Widget - ${graphType}`).should("be.visible");
};

const addCountTrend = (graphType, randomWidgetName, counterType, timeRange) => {
    selectWidget(graphType);
    cy.setInput("Widget Name*", randomWidgetName);
    cy.setInput("Counter Type", counterType);
    cy.get("#timerange").clear().type(`${timeRange}{enter}`);
    cy.findByRole("button", { name: "Save" }).click();
    cy.findByRole("dialog").should("not.exist");
};

const addFieldGraph = (graphType, widgetName, widgetTableType) => {
    selectWidget(graphType);
    cy.findByLabelText("Widget Name*").type(widgetName);
    cy.findByRole("dialog").within(() => {
        cy.findAllByRole("combobox").eq(0).click();
    });
    cy.findByText(widgetTableType).click();
    cy.findByRole("button", { name: "Back" }).should("be.visible");
    cy.findAllByRole("button", { name: "Close" }).last().should("be.visible");
    cy.findByRole("button", { name: "Save" }).click({ force: true });
    cy.findByRole("dialog").should("not.exist");
};

const addStatisticsWidget = (graphType, widgetName, widgetTableType) => {
    selectWidget(graphType);
    cy.findByLabelText("Widget Name*").type(widgetName);
    cy.findByRole("dialog").within(() => {
        cy.findAllByRole("combobox").eq(0).click();
    });
    cy.findByText(widgetTableType).click();
    cy.findByRole("button", { name: "Back" }).should("be.visible");
    cy.findAllByRole("button", { name: "Close" }).last().should("be.visible");
    cy.findByRole("button", { name: "Save" }).click();
    cy.findByRole("dialog").should("not.exist");
};

const addWidget = (graphType, randomWidgetName) => {
    selectWidget(graphType);
    cy.setInput("Widget Name*", randomWidgetName);
    cy.findByRole("button", { name: "Back" }).should("be.visible");
    cy.findAllByRole("button", { name: "Close" }).last().should("be.visible");
    cy.findByRole("button", { name: "Save" }).click({ force: true });
    cy.findByRole("dialog").should("not.exist");
};

const editWidget = (widgetType, widgetName, fieldLabel, newValue) => {
    cy.findByText(widgetName, { exact: false })
        .parents("div.dashboard-widget.card")
        .within(() => {
            cy.findByText(widgetName, { exact: false }).should("be.visible");
            cy.findByTitle("Edit").click();
        });
    cy.findByText("Loading...").should("not.exist");
    cy.findByRole("dialog")
        .should("be.visible")
        .within(() => {
            cy.findByText(`Update Widget - ${widgetType}`, { exact: false }).should("be.visible");
            cy.setInput(fieldLabel, newValue);
            cy.findByRole("button", { name: "Save" }).click();
        });
    cy.findByRole("dialog").should("not.exist");
};

const deleteWidget = (widgetName) => {
    cy.log(`Print widget name: ${widgetName}`);
    cy.contains("div", widgetName, { exact: false })
        .parents("div.dashboard-widget.card")
        .within(() => {
            cy.findByTitle("Delete").click();
        });
    cy.findByRole("dialog")
        .should("be.visible")
        .within(() => {
            cy.findByText("Confirm Delete", { exact: false }).should("be.visible");
            cy.findByText("Are you sure you want to delete this widget?").should("be.visible");
            cy.findByRole("button", { name: "Yes" }).click({ force: true });
        });
    cy.findByRole("dialog").should("not.exist");
    cy.findAllByText(widgetName).should("not.exist");
};

const resetToDefault = () => {
    cy.findByText("Manage Console")
        .click()
        .next("div")
        .within(() => {
            cy.findByText("Restore Default Console").click();
        });
    cy.findByRole("dialog").within(() => {
        cy.get(".modal-title").should("contain.text", "Reset Console");
        cy.findByRole("button", { name: "Reset" }).should("be.enabled").click();
    });
    cy.findByRole("dialog").should("not.exist");
};

const checkDataSourceState = (sourceName, state) => {
    cy.get("[title='Data Feed']")
        .contains(sourceName)
        .then(($element) => {
            const $statusIndicator = $element.parentsUntil("#data-source-list").find(".status-indicator-state").first();
            cy.wrap($statusIndicator).should("have.text", state);
        });
};

const pivotTableWidgetAndVerifyNavigation = (widgetTitle, linkText) => {
    cy.findByText(widgetTitle, { exact: false })
        .should("exist")
        .and("be.visible")
        .closest(".dashboard-widget")
        .within(() => {
            cy.get(".card-body").within(() => {
                cy.get("table").contains("a", linkText).click();
            });
        });
};

const processPivotTableWidgetAndVerifyAllLinks = (widgetTitle) => {
    const failedOptions = [];

    cy.findByText(widgetTitle, { exact: false })
        .should("exist")
        .and("be.visible")
        .closest(".dashboard-widget")
        .within(() => {
            cy.get(".card-body").then(($body) => {
                if ($body.find('.alert[role="alert"]:contains("No data available.")').length > 0) {
                    cy.findByText("No data available.").should("be.visible");
                    cy.wrap(null).as("totalLinks");
                } else {
                    // Just count the links, don't store texts yet
                    cy.get("table").should("be.visible").find("a").its("length").as("totalLinks");
                }
            });
        });

    // Get the link count
    cy.get("@totalLinks").then((totalLinks) => {
        // If no links, exit early
        if (totalLinks <= 0) {
            cy.log(`No links to process in widget "${widgetTitle}"`);
            return;
        }

        // Process each link individually
        for (let i = 0; i < totalLinks; i += 1) {
            cy.log(`Processing link ${i + 1}/${totalLinks}`);

            // Get the text of the current link before clicking
            cy.findByText(widgetTitle, { exact: false })
                .closest(".dashboard-widget")
                .within(() => {
                    cy.get("table")
                        .find("a")
                        .eq(i)
                        .invoke("text")
                        .then((link) => {
                            const linkText = link.trim();
                            cy.wrap(linkText).as("linkText");
                            cy.log(`Clicking link with text: "${linkText}"`);

                            // Click the same link we just read
                            cy.get("table").find("a").eq(i).click();
                        });
                });

            // Wait for loading to finish
            cy.retryTextNotContainCheck(".alert.alert-primary.fade.show", "Loading...");

            // Check for errors
            cy.get("body").then(($body) => {
                const alert = $body.find('[role="alert"]');

                if (alert.length > 0 && alert.hasClass("alert-danger")) {
                    // Get page title or URL to identify the page with error
                    cy.url().then((url) => {
                        failedOptions.push(`${widgetTitle} - Page at ${url}`);
                    });
                } else {
                    // Verify contents based on widget type
                    cy.get("@linkText").then((linkText) => {
                        if (widgetTitle === "Highest Severity Findings From DataBee") {
                            cy.retryTextNotContainCheck(".alert", "Loading DataBee Findings...");
                            cy.findAllByText("DataBee Findings").should("exist");
                            cy.get("canvas[role='img']").should("exist");
                            cy.get("#search-results").should("exist");
                        } else {
                            cy.get("h1.card-title").invoke("text").should("include", linkText.trim());
                        }
                    });
                }
            });

            // Navigate back to console
            cy.navigateTopNav("Console", "Overview");
        }
    });

    // Fail the test if any options caused errors
    cy.then(() => {
        if (failedOptions.length > 0) {
            throw new Error(`The following options caused errors: ${failedOptions.join(", ")}`);
        }
    });
};

const validateWidgetContentDisplay = (widgetTitle) => {
    cy.findByText(widgetTitle, { exact: false })
        .should("exist")
        .and("be.visible")
        .closest(".dashboard-widget")
        .within(() => {
            cy.get(".card-body").then(($body) => {
                if ($body.find('.alert[role="alert"]:contains("No data available")').length > 0) {
                    cy.findByText("No data available.").should("be.visible");
                } else {
                    // Verify canvas element exists for charts
                    cy.get("canvas[role='img']").should("exist");
                }
            });
        });
};

const validateAllWidgetsDisplayWithContent = () => {
    cy.get(".dashboard-widget").each(($dashboard) => {
        cy.wrap($dashboard)
            .find(".card-body")
            .should("exist")
            .find('.alert[role="alert"]:contains("No data available")')
            .should("not.exist");
    });
};

export {
    addCountTrend,
    addFieldGraph,
    addStatisticsWidget,
    addWidget,
    checkDataSourceState,
    createDashboard,
    deleteDashboard,
    deleteWidget,
    editDashboardName,
    editWidget,
    pivotTableWidgetAndVerifyNavigation,
    processPivotTableWidgetAndVerifyAllLinks,
    resetToDefault,
    restoreDefaultConsole,
    validateAllWidgetsDisplayWithContent,
    validateWidgetContentDisplay,
    verifyDashboard,
    verifyOverviewConsoleWidgets,
};
