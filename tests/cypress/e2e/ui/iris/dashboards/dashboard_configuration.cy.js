/// <reference types="cypress"/>
import {
    addFieldGraph,
    addStatisticsWidget,
    addWidget,
    createDashboard,
    deleteDashboard,
    deleteWidget,
    editDashboardName,
    editWidget,
    verifyDashboard,
} from "./dashboard_utils";

// Helper function to generate a unique dashboard name
const generateDashboardName = () => {
    const randomString = Math.random().toString(36).substring(8);
    const randomNumber = Math.floor(Math.random() * (1000 - 100 + 1)) + 100;
    return `MyTestDashboard${randomString}-${randomNumber}`;
};

describe("Tenant: Console", { tags: ["full", "dashboard"] }, () => {
    beforeEach(() => {
        cy.clearCookies();
    });

    afterEach(() => {
        cy.clickLogout();
    });

    it("Admin user can Create a new dashboard and make it default with widgets, verify then deletes", () => {
        const newDashboardName = generateDashboardName();

        cy.loginSession(
            Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
            Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
        );

        createDashboard(newDashboardName, true);
        cy.findByRole("link", { name: "Console" }).then(($el) => {
            if ($el.hasClass("dropdown-toggle")) {
                cy.wrap($el).click();
                cy.findAllByRole("menuitem").first().should("have.text", newDashboardName);
            }
        });

        addWidget("Feed Health", "Test01");
        cy.retryTextNotContainCheck(".alert.alert-info.fade.show", "Loading data...");
        cy.findByText("Test01", { exact: false }).should("be.visible");

        addFieldGraph("Field Graph", "Test Filed Widged Graph2", "Authentication");
        cy.retryTextNotContainCheck(".alert.alert-info.fade.show", "Loading data...");
        editWidget("Field Graph", "Test Filed Widged Graph2", "Widget Name*", "EditedTest01");
        cy.findByText("EditedTest01", { exact: false }).should("be.visible");
        deleteWidget("EditedTest01");

        const editDashName = generateDashboardName();
        editDashboardName(editDashName);
        deleteDashboard(editDashName);
    });

    it("Admin user can create a new dashboard with security finding widgets, verify then edit and delete", () => {
        const newDashboardName = generateDashboardName();

        cy.loginSession(
            Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
            Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
        );

        createDashboard(newDashboardName, false);
        verifyDashboard(newDashboardName);

        addWidget("Security Findings By Entity", "Test01");
        cy.retryTextNotContainCheck(".alert.alert-info.fade.show", "Loading data...");
        cy.findByText("Test01", { exact: false }).should("be.visible");

        editWidget("Security Findings By Entity", "Test01", "Widget Name*", "EditedTest01");
        deleteWidget("EditedTest01");
        deleteDashboard(newDashboardName);
    });

    Object.entries(Cypress.env("credentials").DATABEE_USER_IDS).forEach(([UserName, UserId]) => {
        [
            ["Highest Severities", "search"],
            ["Highest Severities from DataBee", "security/findings"],
        ].forEach((statInfo) => {
            it(`${UserName} should be able to pivot to the ${statInfo[1]} page for ${statInfo[0]}`, () => {
                const newWidgetName = `Test-${statInfo[0]}-${generateDashboardName()}`;

                cy.loginSession(UserId, Cypress.env("credentials")[`DATABEE_${UserName}_PASSWORD`]);

                addStatisticsWidget("Statistics", newWidgetName, statInfo[0]);
                cy.retryTextNotContainCheck(".alert.alert-info.fade.show", "Loading data...");

                cy.contains("div", newWidgetName, { exact: false })
                    .parents("div.dashboard-widget.card")
                    .within(($widget) => {
                        cy.wrap($widget)
                            .find("div.card-body")
                            .then(($cardBody) => {
                                cy.wrap($cardBody).findByText("Loading data...").should("not.exist");

                                cy.wrap($cardBody)
                                    .invoke("text")
                                    .then((text) => {
                                        const trimmedText = text.trim();

                                        if (trimmedText.includes("No data available.")) {
                                            cy.wrap($cardBody).contains("No data available.").should("be.visible");
                                        } else {
                                            cy.wrap($cardBody)
                                                .find("table tbody tr")
                                                .then(($rows) => {
                                                    if ($rows.length > 0) {
                                                        cy.wrap($rows)
                                                            .find("a")
                                                            .then(($links) => {
                                                                if ($links.length > 0) {
                                                                    cy.wrap($links).first().click();
                                                                    cy.url().should("include", statInfo[1]);
                                                                }
                                                            });
                                                    }
                                                });
                                        }
                                    });
                            });
                    });

                cy.navigateTopNav("Console", "Overview");
                deleteWidget(newWidgetName);
            });
        });
    });
});
