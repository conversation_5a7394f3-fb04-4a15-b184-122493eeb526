describe("Fetch API Data and Write to JSON", { tags: ["full"] }, () => {
    const baseUrl = Cypress.env("credentials").DATABEE_UI;
    const dataSourcesUrl = `${baseUrl}api/ingest/data-sources/types/`;
    const configTypesBaseUrl = `${baseUrl}api/ingest/data-sources/types/config-types/`;
    const username = Cypress.env("credentials").DATABEE_USER_IDS.ADMIN;
    const password = Cypress.env("credentials").DATABEE_ADMIN_PASSWORD;
    const jsonFilePath = "cypress/fixtures/dataFeedConfigurations.json";

    beforeEach(() => {
        // Clean up the file at the start of the test to ensure no leftover data from previous runs
        cy.writeFile(jsonFilePath, []);
    });

    it("should fetch data and write to JSON", { tags: "regression" }, () => {
        // Fetch data sources with the given credentials
        cy.fetchDataWithToken(baseUrl, dataSourcesUrl, username, password).then((dataSources) => {
            cy.log("Fetched data sources:", JSON.stringify(dataSources, null, 2));

            if (!dataSources || !Array.isArray(dataSources) || dataSources.length === 0) {
                throw new Error("No valid data feeds fetched from the API");
            }

            const sourceNames = dataSources.map((item) => item.name).filter(Boolean);
            const sourceTypes = dataSources.map((item) => item.type).filter(Boolean);

            // Iterate over each source type to fetch associated configuration data
            cy.wrap(sourceTypes).each((sourceType, index) => {
                const configTypesUrl = `${configTypesBaseUrl}${encodeURIComponent(sourceType)}`;

                // Fetch config data for each source type
                cy.fetchDataWithToken(baseUrl, configTypesUrl, username, password).then((configData) => {
                    if (!configData || !Array.isArray(configData)) {
                        cy.log(`No valid config data for source: ${sourceNames[index]}`);
                        return;
                    }

                    const titles = configData.map((config) => config.title).filter(Boolean);

                    if (titles.length === 0) {
                        cy.log(`No titles found for source: ${sourceNames[index]}`);
                        return;
                    }

                    // Prepare the configuration data
                    const configurations = {};
                    titles.forEach((title, idx) => {
                        configurations[`option${idx + 1}`] = title || "";
                    });

                    // Collect the result as an object
                    const result = { sourcename: sourceNames[index], configurations };

                    // Append the result to the JSON file
                    cy.readFile(jsonFilePath).then((existingData = []) => {
                        const updatedData = [...existingData, result];
                        cy.writeFile(jsonFilePath, updatedData);
                    });
                });
            });
        });
    });
});
