import { checkDataSourceState } from "../dashboards/dashboard_utils";
import { deleteDataCollector, gotoDataCollector } from "../data_collector/data_collector_utils";
import { goToDataTab } from "../navigation_rbac/navigation_utils";

import {
    configureFilters,
    configureInputsAndTags,
    createNewDataCollectorDataSource,
    deleteDataSource,
    getDataCollectorForDataSource,
    getDataSources,
    gotoAddNewDataSourcePage,
    searchAndSelectSource,
    setDataSourceEnabled,
    verifyDataSourceCardDetails,
    verifyNewDataSourceWithDataCollector,
    waitForDataFeedCountToUpdate,
    waitForNoDataFeeds,
    waitForStatusToBe,
} from "./data_utils";

describe("Tenant: Users can create data collector feeds.", { tags: ["smoke", "data", "full"] }, () => {
    let createdSources = [];

    beforeEach(() => {
        cy.clearCookies();
        cy.visit(Cypress.env("credentials").DATABEE_UI);
    });

    afterEach(() => {
        cy.clickLogout();
    });

    it(
        "Admin Create Data collector data feed with Log Type, Format, Mode, Port, Tags, Filters, Port then Enable, Disable & Delete datasource.",
        { tags: "smoke" },
        () => {
            cy.manualLogin(
                Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
                Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
            );
            cy.wrap(getDataCollectorForDataSource()).then((dataCollectorName) => {
                gotoAddNewDataSourcePage();
                cy.fixture("datasources.json").then((datasources) => {
                    const newDataSource = { ...datasources.datacollector_datasource };
                    newDataSource.name += `${Math.random().toString(36).substring(4)}`;

                    const dataSourceDetails = createNewDataCollectorDataSource(newDataSource, dataCollectorName);
                    cy.findByRole("button", { name: "Next" }).click();
                    // configure data feed tags
                    cy.findByText("No Tags configured. Click add to start configuring some.").should("be.visible");
                    cy.findByRole("button", { name: "Add" }).should("be.visible");
                    configureInputsAndTags(newDataSource);
                    cy.findByRole("button", { name: "Next" }).click();
                    cy.findByRole("button", { name: "Submit" }).should("exist");
                    // configure data feed filter
                    cy.findByText("No Filters configured. Click add to start configuring some.").should("be.visible");
                    cy.findByRole("button", { name: "Add" }).should("be.visible");
                    configureFilters(newDataSource);
                    cy.findByRole("button", { name: "Submit" }).click();
                    goToDataTab();
                    waitForStatusToBe("Healthy", dataSourceDetails.sourceName);
                    searchAndSelectSource(dataSourceDetails.sourceName);
                    verifyDataSourceCardDetails(
                        dataSourceDetails.ownerName,
                        dataSourceDetails.ownerEmail,
                        "Healthy",
                        Cypress.env("credentials").DATABEE_USER_ROLES.Admin
                    );
                    setDataSourceEnabled(dataSourceDetails.sourceName, false);
                    cy.retryTextNotContainCheck(".alert.alert-primary.fade.show", "Loading...");
                    waitForStatusToBe("Disabled", dataSourceDetails.sourceName);
                    checkDataSourceState(dataSourceDetails.sourceName, "Disabled");
                    setDataSourceEnabled(dataSourceDetails.sourceName, true);
                    cy.retryTextNotContainCheck(".alert.alert-primary.fade.show", "Loading...");
                    waitForStatusToBe("Healthy", dataSourceDetails.sourceName);
                    checkDataSourceState(dataSourceDetails.sourceName, "Healthy");
                    // delete and remove from the list
                    cy.get("#ingest-data-metrics-overview li.list-inline-item")
                        .invoke("text")
                        .then((text) => {
                            const match = text.match(/(\d+)\s+Data\s+Feed[s]?/i);
                            if (!match) {
                                throw new Error(`Unable to parse Data Feed count from text: "${text}"`);
                            }

                            const initialCount = parseInt(match[1], 10);
                            cy.then(() => deleteDataSource(dataSourceDetails.sourceName));
                            cy.then(() => waitForDataFeedCountToUpdate(initialCount));
                        });
                    createdSources = createdSources.filter((src) => src !== dataSourceDetails.sourceName);
                    gotoDataCollector();
                    deleteDataCollector(dataCollectorName);
                });
            });
        }
    );
});

describe(
    "DataBee Web Application: Admin User can create new data collector data feeds",
    { tags: ["full", "data"] },
    () => {
        let defaultDataCollectorName;
        const datasourceDetails = {};
        beforeEach(() => {
            cy.clearCookies();
            cy.visit(Cypress.env("credentials").DATABEE_UI);
        });

        it("Configure Data Collector Data Feeds", { tags: ["e2e_setup"] }, () => {
            cy.manualLogin(
                Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
                Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
            );
            cy.wrap(getDataCollectorForDataSource())
                .then((collectorName) => {
                    defaultDataCollectorName = collectorName;
                    return getDataSources();
                })
                .then((dataSources) => {
                    const collectorDataSources = dataSources.map((dataSource) => ({
                        ...dataSource,
                        name: `${dataSource.name}_${defaultDataCollectorName}`,
                    }));
                    const setDataSources = (sourceName, sourceId) => {
                        datasourceDetails[sourceName] = sourceId;
                    };
                    collectorDataSources.forEach((dataSource) => {
                        gotoAddNewDataSourcePage();
                        const { ownerName, ownerEmail } = createNewDataCollectorDataSource(
                            dataSource,
                            defaultDataCollectorName
                        );
                        const datasourceDetail = { ...dataSource, ownerName, ownerEmail };
                        cy.findByRole("button", { name: "Next" }).click();
                        configureInputsAndTags(datasourceDetail);
                        cy.findByRole("button", { name: "Next" }).click();
                        cy.findByRole("button", { name: "Submit" }).should("exist");
                        configureFilters(datasourceDetail);
                        cy.findByRole("button", { name: "Submit" }).click();
                        verifyNewDataSourceWithDataCollector(datasourceDetail, setDataSources);
                    });
                });
            const filePath = "cypress/fixtures/datasources.json";
            cy.writeFile(filePath, datasourceDetails);
        });

        it("Remove Data Collector Data Feeds", { tags: ["e2e_cleanup"] }, () => {
            cy.manualLogin(
                Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
                Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
            );
            getDataSources().then((dataSources) => {
                goToDataTab();
                if (Cypress.env("collectorName")) {
                    defaultDataCollectorName = Cypress.env("collectorName");
                }
                // delete data feeds
                dataSources.forEach((dataSource) => {
                    deleteDataSource(`${dataSource.name}_${defaultDataCollectorName}`);
                });
                waitForNoDataFeeds();
            });
            if (!Cypress.env("collectorName")) {
                cy.navigateTopRightNav("Configuration", "System");
                cy.findByRole("link", { name: "Data Collectors" }).click();
                cy.findAllByText(defaultDataCollectorName)
                    .parent()
                    .first()
                    .within(($el) => {
                        cy.wrap($el).click();
                    });
                cy.findByRole("button", { name: "Delete" }).should("exist").click();
                cy.findAllByText("Delete").eq(1).click();
                cy.findByText(defaultDataCollectorName).should("not.exist");
            }
        });
    }
);
