import {
    apiIngestFeedConfigHistory,
    createNewDataSource,
    deleteDataSource,
    gotoAddNewDataSourcePage,
    searchAndSelectSource,
    verifyDataSourceCardDetails,
    waitForStatusToBe,
} from "./data_utils";

describe("Tenant: Users can create api ingest feeds.", { tags: ["data", "full"] }, () => {
    let createdSources = [];

    beforeEach(() => {
        cy.clearCookies();
        cy.visit(Cypress.env("credentials").DATABEE_UI);
    });

    afterEach(() => {
        cy.clickLogout();
    });

    it("API Ingest Feed Configuration: Basic, Bearer Token, OAuth2", () => {
        cy.manualLogin(
            Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
            Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
        );

        // ----------- Test Case 1: Basic Auth (Zendesk) -----------
        gotoAddNewDataSourcePage();
        const zendesk = createNewDataSource("Zendesk", "API Ingest", "us-east-1");
        createdSources.push(zendesk.sourceName);
        waitForStatusToBe("Healthy", zendesk.sourceName);
        searchAndSelectSource(zendesk.sourceName);
        verifyDataSourceCardDetails(
            zendesk.ownerName,
            zendesk.ownerEmail,
            "Healthy",
            Cypress.env("credentials").DATABEE_USER_ROLES.Admin
        );
        apiIngestFeedConfigHistory(zendesk.sourceName, zendesk.ownerName, zendesk.ownerEmail, "Basic");

        // ----------- Test Case 2: Bearer Token (1password) -----------
        gotoAddNewDataSourcePage();
        const onePassword = createNewDataSource("1password", "API Ingest", "us-east-1", "Bearer Token");
        createdSources.push(onePassword.sourceName);
        waitForStatusToBe("Healthy", onePassword.sourceName);
        searchAndSelectSource(onePassword.sourceName);
        verifyDataSourceCardDetails(
            onePassword.ownerName,
            onePassword.ownerEmail,
            "Healthy",
            Cypress.env("credentials").DATABEE_USER_ROLES.Admin
        );
        apiIngestFeedConfigHistory(
            onePassword.sourceName,
            onePassword.ownerName,
            onePassword.ownerEmail,
            "Bearer Token"
        );

        // ----------- Test Case 3: OAuth2 (Workday) -----------
        gotoAddNewDataSourcePage();
        const workday = createNewDataSource("Workday", "API Ingest", "us-east-1", "OAuth2");
        createdSources.push(workday.sourceName);
        waitForStatusToBe("Healthy", workday.sourceName);
        searchAndSelectSource(workday.sourceName);
        verifyDataSourceCardDetails(
            workday.ownerName,
            workday.ownerEmail,
            "Healthy",
            Cypress.env("credentials").DATABEE_USER_ROLES.Admin
        );
        apiIngestFeedConfigHistory(workday.sourceName, workday.ownerName, workday.ownerEmail, "OAuth2");
        // Cleanup: delete all created sources
        [...createdSources].forEach((src) => deleteDataSource(src));
        createdSources = [];
    });

    it("API Ingest feed validation checks", () => {
        // Data sources with their specific authentication methods
        const dataSources = [
            {
                sourceName: "Workday",
                configType: "API Ingest",
                authMethod: {
                    type: "OAuth2",
                    configForm: {
                        "Refresh Token *": "testRefreshToken", // Optional
                        "Client Key *": "testClientKey",
                        "Client Secret *": "testClientSecret",
                        "Token URL *": "https://wd2-impl-services1.workday.com/ccx/oauth2/test_tenant/token",
                        "Tenant Name *": "stg-test",
                    },
                    validationChecks: {
                        requiredFields: [
                            "Tenant Name *",
                            "Client Key *",
                            "Client Secret *",
                            "Token URL *",
                            "Refresh Token *",
                        ],
                    },
                },
            },
            {
                sourceName: "Zendesk",
                configType: "API Ingest",
                authMethod: {
                    type: "Basic",
                    configForm: {
                        "Username *": "testZendeskUsername",
                        "Password *": "testZendeskPassword",
                    },
                    validationChecks: {
                        requiredFields: ["Username *", "Password *"],
                    },
                },
            },
            {
                sourceName: "1password",
                configType: "API Ingest",
                authMethod: {
                    type: "Bearer Token",
                    configForm: {
                        "Token *": "testBearerTokenForOnePaassword",
                    },
                    validationChecks: {
                        requiredFields: ["Token *"],
                    },
                },
            },
        ];

        // Login and navigate to data source page
        cy.manualLogin(
            Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
            Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
        );
        const ingestSource = [];
        // Loop through data sources
        dataSources.forEach((dataSource) => {
            // Generate random test data
            const testrandomString = Math.random().toString(36).substring(7);
            const randomSourceName = `testSourceName${testrandomString}`;
            const randomOwnerName = `testOwnerName${testrandomString}`;
            const randomOwnerEmail = `testOwnerEmail${testrandomString}@bluvector.io`;

            // Select data source and config type
            gotoAddNewDataSourcePage();
            cy.retryTextNotContainCheck(".alert.alert-primary.fade.show", "Loading...");
            cy.findByPlaceholderText("Search Data Feed Types").click().type(dataSource.sourceName);
            cy.findByText(dataSource.sourceName).click();
            cy.findByText(dataSource.configType).click();
            ingestSource.push(randomSourceName);
            // Initial configuration form
            const initialConfigForm = {
                "Data Feed Name *": randomSourceName,
                "Owner Name *": randomOwnerName,
                "Owner E-mail *": randomOwnerEmail,
            };

            // Update initial configuration
            cy.updateForm(initialConfigForm);

            // Validation for initial form
            cy.findByLabelText("Data Feed Name *").clear();
            cy.findByLabelText("Owner Name *").clear();
            cy.findByLabelText("Owner E-mail *").clear();

            // Check required field and email validation
            cy.findAllByText("Required!").should("have.length", 2);
            cy.findByText("Email is invalid").should("be.visible");
            cy.findByRole("button", { name: "Next" }).should("be.disabled");

            // Test invalid email
            cy.updateForm({ ...initialConfigForm, "Owner E-mail *": "test" });
            cy.findByText("Email is invalid").should("be.visible");
            cy.findByRole("button", { name: "Next" }).should("be.disabled");

            // Correct email
            cy.setInput("Owner E-mail *", randomOwnerEmail);

            // Proceed to next step
            cy.findByRole("button", { name: "Next" }).click();

            // Validate authentication method
            const { authMethod } = dataSource;

            // Verify available authentication methods based on data source
            if (dataSource.sourceName === "Workday") {
                cy.get("[class*='react-select__single-value']")
                    .should("contain", "OAuth2")
                    .and("not.contain", "Basic")
                    .and("not.contain", "Bearer Token");
            } else if (dataSource.sourceName === "Zendesk") {
                cy.get("[class*='react-select__single-value']")
                    .should("contain", "Basic")
                    .and("not.contain", "OAuth2")
                    .and("not.contain", "Bearer Token");
            } else if (dataSource.sourceName === "1Password") {
                cy.get("[class*='react-select__single-value']")
                    .should("contain", "Bearer Token")
                    .and("not.contain", "Basic")
                    .and("not.contain", "OAuth2");
            }

            // Select the specific authentication method
            cy.setReactSelectOption("[class*='react-select__input-container']", authMethod.type);

            // Submit button should be disabled initially
            cy.findByRole("button", { name: "Submit" }).should("be.disabled");

            // Clear required inputs
            const requiredInputs = authMethod.validationChecks.requiredFields;
            requiredInputs.forEach((input) => {
                cy.findByLabelText(input).clear();
            });

            // Check required field validations
            cy.findAllByText("Required!").should("have.length", requiredInputs.length);
            cy.findByRole("button", { name: "Submit" }).should("be.disabled");

            // Partial fill - only required fields
            const requiredFieldsConfig = {};
            requiredInputs.forEach((field) => {
                requiredFieldsConfig[field] = dataSource.authMethod.configForm[field];
            });

            // Update form with required fields
            cy.updateForm(requiredFieldsConfig);

            // Verify submit button is now enabled
            cy.findByRole("button", { name: "Submit" }).should("not.be.disabled");
        });

        cy.navigateTopNav("Data", "Data Feeds");
        // Delete the created data source
        ingestSource.forEach((src) => deleteDataSource(src));
    });
});
