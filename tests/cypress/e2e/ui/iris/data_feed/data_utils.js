// <reference types="cypress"/>
import yaml from "js-yaml";

import { gotoAddNewDataCollector } from "../data_collector/data_collector_utils";
import { goToDataTab } from "../navigation_rbac/navigation_utils";

const searchAndClickDataSource = (sourceName) => {
    cy.findByPlaceholderText("Search Data Feeds").click({ force: true }).clear().type(sourceName);
    cy.findByText(sourceName)
        .parent()
        .within(($el) => {
            cy.wrap($el).click();
        });
};

const createNewDataSource = (sourceName, configType, formData, authMethod = "Basic", shouldSubmit = true) => {
    const testrandomString = Math.random().toString(36).substring(7);
    const randomSourceName = `testSourceName${testrandomString}`;
    const randomOwnerName = `testOwnerName${testrandomString}`;
    const randomOwnerEmail = `testOwnerEmail${testrandomString}@bluvector.io`;

    cy.findByPlaceholderText("Search Data Feed Types").click().type(sourceName);
    cy.findByText(sourceName).click();
    cy.findByText(configType).click();

    cy.findByLabelText("Data Feed Name *").type(randomSourceName);
    cy.findByLabelText("Owner Name *").type(randomOwnerName);
    cy.findByLabelText("Owner E-mail *").type(randomOwnerEmail);

    switch (configType) {
        case "AWS S3": {
            Object.entries(formData).forEach(([key, value]) => {
                if (value === true) {
                    cy.setInput(key);
                } else {
                    cy.setInput(key, value);
                }
            });
            const S3bucket = Cypress.env("S3_BUCKET_NAME");

            cy.task("createBucket").then((res) => {
                if (res.error) {
                    Cypress.log({
                        name: "createBucket",
                        message: `Error creating bucket: ${JSON.stringify(res.error)}`,
                        consoleProps: () => res.error,
                    });
                    throw new Cypress.Error(`Failed to create bucket: ${JSON.stringify(res.error)}`);
                } else {
                    cy.findByLabelText("S3 Bucket Name *").type(S3bucket);
                }
            });
            cy.get('[id="reference-link"]').then((link) => {
                cy.request(link.prop("href")).its("status").should("eq", 200);
            });
            cy.findByRole("button", { name: "Next" }).click();
            cy.intercept("api/ingest/data-sources/**").as("getPolicy");
            cy.wait("@getPolicy");
            cy.findByText("Copy the IAM policy and KMS policy to apply to your AWS S3 bucket").should("be.visible");
            cy.get('[id="reference-link"]').then((link) => {
                cy.request(link.prop("href")).its("status").should("eq", 200);
            });
            cy.findByRole("button", { name: "Copy Content" }) // Select the button element
                .closest(".copyable-text-blob") // go to the wrapper div
                .find("pre") // find the <pre> tag inside it
                .invoke("text")
                .then((policyParams) => {
                    cy.task("putS3BucketPolicy", policyParams).then(() => {
                        cy.findByRole("button", { name: "Next" }).click();
                    });
                });

            cy.findByText("Setup AWS S3 bucket notification").should("be.visible");
            cy.get('[id="reference-link"]').then((link) => {
                cy.request(link.prop("href")).its("status").should("eq", 200);
            });
            cy.findByRole("button", { name: "Copy Content" }) // Select the button element
                .closest(".copyable-text-blob") // go to the wrapper div
                .find("pre") // find the <pre> tag inside it
                .invoke("text") // Get the text content of the div
                .then((QueueArnTxt) => {
                    const queueConfig = {
                        Id: `${S3bucket}-${randomSourceName}`,
                        Events: ["s3:ObjectCreated:*", "s3:ObjectRemoved:*"],
                        QueueArn: QueueArnTxt,
                    };
                    cy.task("createS3EventNotification", queueConfig).then(() => {
                        cy.get('[class*="step done"]').should("have.length", 2);
                        cy.findByRole("button", { name: "Test Connection" }).click();
                        cy.validateToastMessage("Connection successful!");
                        cy.findByRole("button", { name: "Submit" }).click();
                    });
                });
            return {
                sourceName: randomSourceName,
                ownerName: randomOwnerName,
                ownerEmail: randomOwnerEmail,
                bucketName: S3bucket,
            };
        }
        case "API Ingest": {
            cy.findByRole("button", { name: "Next" }).click();

            switch (authMethod) {
                case "Bearer Token": {
                    cy.setReactSelectOption("[class*='react-select__input-container']", "Bearer Token");
                    cy.setInput("Token *", "testBearerToken");
                    break;
                }
                case "OAuth2": {
                    cy.setReactSelectOption("[class*='react-select__input-container']", "OAuth2");

                    // Define oauth2ConfigForm based on formData or fallback to default values
                    // If formData is a valid non-empty object, use it; otherwise use default config
                    const oauth2ConfigForm =
                        typeof formData === "object" && formData !== null && Object.keys(formData).length > 0
                            ? formData
                            : {
                                  "Refresh Token *": "testRefreshToken",
                                  "Client Key *": "testClientKey",
                                  "Client Secret *": "testClientSecret",
                                  "Tenant Name *": "stg-test",
                                  "Token URL *": "https://wd2-impl-services1.workday.com/ccx/oauth2/test_tenant/token",
                              };

                    cy.updateForm(oauth2ConfigForm);
                    break;
                }
                default: {
                    const basicAuthConfigForm = {
                        "Username *": Cypress.env("credentials").API_INGEST_BASIC.Username,
                        "Password *": Cypress.env("credentials").API_INGEST_BASIC.Password,
                        "API Base URL *": Cypress.env("credentials").API_INGEST_BASIC.BaseUrl,
                    };
                    cy.updateForm(basicAuthConfigForm);
                    break;
                }
            }

            if (shouldSubmit) {
                cy.findByRole("button", { name: "Submit" }).click();
            }

            return {
                sourceName: randomSourceName,
                ownerName: randomOwnerName,
                ownerEmail: randomOwnerEmail,
            };
        }
        default:
            break;
    }

    return {
        sourceName: randomSourceName,
        ownerName: randomOwnerName,
        ownerEmail: randomOwnerEmail,
    };
};

const validateNewDataSourceFlow = (sourceName, type, dataCollectorType = null) => {
    const awsS3FormData = {
        "Delete objects in the S3 bucket after read": true,
        "AWS Region *": Cypress.env("AWS_REGION"),
        "Compression *": "none",
        "Content Type *": "JSON",
    };
    const testrandomString = Math.random().toString(36).substring(7);
    const randomSourceName = `testSourceName${testrandomString}`;
    const randomOwnerName = `testOwnerName${testrandomString}`;
    const randomOwnerEmail = `testOwnerEmail${testrandomString}@databee.ai`;

    cy.findByPlaceholderText("Search Data Feed Types").click().type(sourceName);
    cy.findByText(sourceName).click();
    cy.findByText("How would you like to configure your data feed?").should("exist");
    cy.get("button h3").then(($el) => {
        const text = $el.text();
        expect(text).to.contain(type);
    });

    function configureDataFeedInformation() {
        cy.findByLabelText("Data Feed Name *").type(randomSourceName);
        cy.findByLabelText("Owner Name *").type(randomOwnerName);
        cy.findByLabelText("Owner E-mail *").type(randomOwnerEmail);
    }
    function validateDataFeedFormNavigation(stepWizardTitle) {
        cy.findByRole("button", { name: "Next" }).click();
        cy.contains("li", stepWizardTitle).should("have.class", "done").and("not.have.class", "active");
        cy.findAllByRole("button", { name: "Back" }).eq(1).click();
        cy.contains("li", stepWizardTitle).should("have.class", "active").and("not.have.class", "done");
        cy.findByRole("button", { name: "Back" }).click();
    }

    if (type !== "Data Collector") {
        cy.findByText(type).click();
        configureDataFeedInformation();
    } else if (type === "Data Collector") {
        cy.findByText(type).click();
    }
    switch (type) {
        case "API Ingest":
        case "Azure Blob":
        case "HTTP Collector":
            if (type === "HTTP Collector") {
                validateDataFeedFormNavigation("Provide Basic Information");
            } else {
                validateDataFeedFormNavigation("Configure Data Feed");
            }
            break;
        case "Data Collector":
            cy.findByText(dataCollectorType).click();
            configureDataFeedInformation();
            cy.findByText("Collector *")
                .next("div")
                .within(() => {
                    cy.get(".react-select__value-container").click();
                    cy.findByText("Linux Collector Syslog").click();
                });
            validateDataFeedFormNavigation("Configure Data Feed");
            cy.findByRole("button", { name: "Back" }).click();
            break;
        case "AWS S3":
            cy.findByLabelText("S3 Bucket Name *").type(Cypress.env("S3_BUCKET_NAME"));
            Object.entries(awsS3FormData).forEach(([key, value]) => {
                if (value === true) {
                    cy.setInput(key);
                } else {
                    cy.setInput(key, value);
                }
            });
            validateDataFeedFormNavigation("Configure Data Feed");
            break;
        default:
            cy.log("Invalid config type!");
            break;
    }

    return {
        sourceName: randomSourceName,
    };
};

const deleteDataSource = (sourceName) => {
    searchAndClickDataSource(sourceName);
    // eslint-disable-next-line no-use-before-define -- exception since it's used by many functions
    waitForElementToBeEnabled("Delete", sourceName);
    cy.findByTitle("Delete").click();
    cy.on("window:alert", () => true);
    cy.contains("Delete Data Feed");
    cy.findByRole("button", { name: "Delete Data Feed" }).click();
    cy.contains(`Deleting ${sourceName}.`).should("be.visible");
};

const verifyFeedConfigHistory = (sourceName) => {
    cy.findByText(sourceName)
        .parent()
        .within(($el) => {
            cy.wrap($el).click();
        });
    cy.findByTitle("Data Feed Timeline").click();
    cy.contains(`Data Feed Timeline - ${sourceName}`);
    cy.getUserInfo();
    cy.then(() => {
        cy.contains(`${Cypress.env("credentials").DATABEE_USER_IDS.ADMIN} deployed a feed`);
    });
    cy.get(".modal-content").within(() => {
        cy.findByRole("table")
            .find("tbody tr")
            .first()
            .within(() => {
                cy.findByRole("button").click();
            });
    });
};

const apiIngestFeedConfigHistory = (sourceName, ownerName, ownerEmail, authType) => {
    verifyFeedConfigHistory(sourceName);

    cy.get(".pretty-json-container")
        .invoke("text")
        .then((text) => {
            expect(text).to.contain(ownerName);
            expect(text).to.contain(ownerEmail);
            expect(text).to.contain(authType);
        });
    cy.findByLabelText("Close").click();
    cy.get("body").type("{esc}");
};

const s3FeedConfigHistory = (sourceName, ownerName, ownerEmail, bucketName) => {
    verifyFeedConfigHistory(sourceName);

    cy.get(".pretty-json-container")
        .invoke("text")
        .then((text) => {
            expect(text).to.contain(ownerName);
            expect(text).to.contain(ownerEmail);
            expect(text).to.contain(bucketName);
        });
};

const gotoAddNewDataSourcePage = () => {
    goToDataTab();
    cy.findByRole("button", { name: "Add New Data Feed" }).click();
};

const searchAndSelectSource = (sourceName) => {
    cy.findByPlaceholderText("Search Data Feeds")
        .click({ force: true })
        .clear()
        .type(sourceName.includes("testSourceName") ? sourceName.split("testSourceName")[1] : sourceName);

    cy.retryTextNotContainCheck(".status-indicator-state", "Processing");

    cy.findByText(sourceName)
        .parent()
        .within(($el) => {
            cy.wrap($el).click();
        });
};

const waitForElementToBeEnabled = (title, sourceName, maxRetries = 30, interval = 5000) => {
    let retries = 0;

    function check() {
        if (retries >= maxRetries) {
            throw new Error(`Element with title: '${title}' was still disabled after ${maxRetries} retries`);
        }

        const checkIfElementInDialogIsEnabled = (titleText) =>
            cy
                .findByRole("dialog")
                .findByTitle(titleText)
                .then(($el) => $el.is(":enabled"));

        checkIfElementInDialogIsEnabled(title).then((isEnabled) => {
            if (!isEnabled) {
                cy.log(`Element with title: '${title}' not enabled after ${retries} retries, refreshing...`);
                cy.reload();
                // eslint-disable-next-line cypress/no-unnecessary-waiting -- wait is needed to perform interval checks
                cy.wait(interval);
                searchAndSelectSource(sourceName);
                retries += 1;
                check();
            } else {
                cy.log(`Element with title: '${title}' is now enabled.`);
            }
        });
    }

    check();
};

const waitForDataFeedToDelete = (textToFind, maxRetries = 30, interval = 3000) => {
    let retries = 0;

    function check() {
        cy.get("#ingest-page-sources")
            .should("exist")
            .then(() => {
                cy.document().then((document) => {
                    const dataFeed = document.querySelector(".col-7.col-sm-7.col-md-7");
                    if (dataFeed) {
                        cy.get(".col-7.col-sm-7.col-md-7").then(($el) => {
                            if ($el.length && $el.text().includes(textToFind)) {
                                if (retries < maxRetries) {
                                    cy.log(`Data Feed '${textToFind}' found, refreshing page...`);
                                    cy.reload();
                                    // eslint-disable-next-line cypress/no-unnecessary-waiting -- wait is needed to perform interval checks
                                    cy.wait(interval);
                                    retries += 1;
                                    check();
                                } else {
                                    cy.log(
                                        `Max retries reached. Data Feed with name: '${textToFind}' is still present.`
                                    );
                                }
                            } else {
                                cy.log(`Data Feed with name: '${textToFind}' not found.`);
                            }
                        });
                    } else {
                        cy.log("No Data Feed was found.");
                    }
                });
            });
    }

    check();
};

const waitForStatusToBe = (status, sourceName, maxRetries = 30, interval = 5000) => {
    let retries = 0;

    function check() {
        if (retries >= maxRetries) {
            throw new Error(`Data Feed Status did not change to: '${status}' after ${maxRetries} retries`);
        }

        cy.findByPlaceholderText("Search Data Feeds")
            .click({ force: true })
            .clear()
            .type(sourceName)
            .then(() => {
                cy.contains(".list-item", sourceName)
                    .find("[class^='status-indicator-state status-indicator-state-']")
                    .invoke("text")
                    .then((statusText) => {
                        const currentStatus = statusText.trim();
                        const isMatched = currentStatus === status;

                        cy.log(`Checking if Data Feed Status is: '${status}'`);
                        cy.log(`Current Data Feed Status: '${currentStatus}'`);

                        if (isMatched) {
                            cy.log(`✅ Data Feed Status changed to: '${status}' after ${retries} retries.`);
                        } else {
                            cy.log(`🔁 Status not matched after ${retries} retries, refreshing...`);
                            retries += 1;
                            cy.reload();
                            cy.wait(interval * 1).then(() => check());
                        }
                    });
            });
    }

    check();
};

const verifyDataSourceCardDetails = (ownerName, ownerEmail, status, userName, closeDialog = true) => {
    cy.findByRole("dialog").within(() => {
        if (userName === "SECURITY_ANALYST") {
            cy.findByTitle("Edit").should("not.exist");
            cy.findByTitle("Delete").should("not.exist");
            cy.findByTitle("Data Feed Timeline").should("not.exist");
        } else if (status === "Disabled") {
            cy.findByTitle("Edit").should("be.disabled");
            cy.findByTitle("Delete").should("be.visible").and("be.enabled");
            cy.findByTitle("Data Feed Timeline").should("be.visible").and("be.enabled");
        } else {
            cy.findByTitle("Edit").should("be.enabled");
            cy.findByTitle("Delete").should("be.visible").and("be.enabled");
            cy.findByTitle("Data Feed Timeline").should("be.visible").and("be.enabled");
        }
        cy.findAllByText(status).scrollIntoView({ ensureScrollable: false }).should("be.visible");
        cy.findAllByText(ownerName).scrollIntoView({ ensureScrollable: false }).should("be.visible");
        cy.findAllByText(ownerEmail).scrollIntoView({ ensureScrollable: false }).should("be.visible");
    });

    if (closeDialog) {
        cy.get("body").type("{esc}");
    }
};

/**
 * Creates a new data collector data feed with the given parameters.
 *
 * @param {object} dataSource - The data feed object.
 * @param {string} feedType - The name of the data feed.
 * @param {string} dataCollector - The data collector name.
 * @param {string} configType - The configuration type.
 */
const createNewDataCollectorDataSource = (dataSource, dataCollector) => {
    const testrandomString = Math.random().toString(36).substring(7);
    const randomOwnerName = `testOwnerName${testrandomString}`;
    const randomOwnerEmail = `testOwnerEmail${testrandomString}@bluvector.io`;

    const { configType, logSource, label: feedType, name } = dataSource;

    cy.findByPlaceholderText("Search Data Feed Types").click().type(feedType);
    cy.findByText(feedType).click();
    cy.findByText(configType).click();
    cy.findByText(logSource).click();

    cy.updateForm({
        "Data Feed Name *": name,
        "Owner Name *": randomOwnerName,
        "Owner E-mail *": randomOwnerEmail,
    });

    cy.findByLabelText("Collector *").closest(".react-select__control").click();
    cy.findByRole("option", { name: dataCollector }).click({ force: true });

    return {
        sourceName: name,
        ownerName: randomOwnerName,
        ownerEmail: randomOwnerEmail,
    };
};

/**
 * Function to verify the data quality summary for a specific data feed.
 *
 * @param {object} dataSource - The data feed object to verify data quality for.
 */
const verifyDataQualitySummaryPageLabels = (dataSource) => {
    cy.findByRole("button", { name: "View Data Quality Summary" }).click({ force: true });
    cy.findByText("Bytes Ingested").should("be.visible");
    cy.findByText("Records Mapped").should("be.visible");
    cy.findByText(dataSource.name).should("be.visible");
    cy.findByText("Feed Mapping Efficiency").should("be.visible");
    cy.findByText("Records Ingested").should("be.visible");
    cy.findByText("Feed Bandwidth").should("be.visible");
    cy.findByText("Data Last Ingested").should("be.visible");
    cy.findByText("Owner").should("be.visible");
};

/**
 * Function to verify the data quality metrics for a specific data feed.
 *
 * @param {string} bytesIngested - The expected value for Bytes Ingested.
 * @param {string} recordsMapped - The expected value for Records Mapped.
 * @param {string} feedMappingEfficiency - The expected value for Feed Mapping Efficiency.
 * @param {string} recordsIngested - The expected value for Records Ingested.
 * @param {string} ownerEmail - The expected value for Owner.
 */
const verifyDataQualityMetrics = (
    bytesIngested,
    recordsMapped,
    feedMappingEfficiency,
    recordsIngested,
    ownerEmail,
    maxAttempts = 5
) => {
    const selectors = [
        { label: "Bytes Ingested", expected: bytesIngested },
        { label: "Records Mapped", expected: recordsMapped },
        { label: "Feed Mapping Efficiency", expected: feedMappingEfficiency },
        { label: "Records Ingested", expected: recordsIngested },
        { label: "Owner", expected: ownerEmail },
    ];

    function checkAllMetrics(attempt = 1) {
        cy.findByRole("button", { name: "View Data Quality Summary" }).click({ force: true });

        cy.wrap(null).then(() => {
            const actuals = [];
            return Cypress.Promise.each(selectors, (item) =>
                cy
                    .findByText(item.label, { timeout: 180000 })
                    .should("be.visible")
                    .parent()
                    .next()
                    .find("p")
                    .invoke("text")
                    .then((text) => {
                        actuals.push({ label: item.label, expected: item.expected, actual: text });
                    })
            ).then(() => {
                const failed = actuals.find((a) => a.expected !== a.actual);
                if (failed && attempt < maxAttempts) {
                    cy.log(
                        `${failed.label} not as expected (got '${failed.actual}'), reloading page and retrying (attempt ${attempt})`
                    );
                    cy.reload();
                    checkAllMetrics(attempt + 1);
                } else if (failed) {
                    throw new Error(
                        `${failed.label} did not match expected value after ${maxAttempts} attempts (last value: '${failed.actual}')`
                    );
                } else {
                    // All metrics matched, assert for all
                    actuals.forEach((item) => {
                        cy.findByText(item.label).parent().next().find("p").should("have.text", item.expected);
                    });
                }
            });
        });
    }

    checkAllMetrics();
};

/**
 * Function to verify a new data feed with a data collector.
 *
 * @param {object} dataSource - The data feed object containing name, logSource, and port.
 */
const verifyNewDataSourceWithDataCollector = (dataSource, setDataSources) => {
    const { name, logSource, ownerName, ownerEmail } = dataSource;
    waitForStatusToBe("Healthy", name);
    cy.findByText(name)
        .parent()
        .within(($el) => {
            cy.wrap($el).click({ force: true });
        });

    cy.findByText("Owner").next().should("contain.text", ownerName);
    cy.findByText("Contact").next().should("contain.text", ownerEmail);
    cy.findByText("Ingest Type").next().should("contain.text", `Data Collector(${logSource})`);

    cy.findByText("ID").next().should("exist");
    cy.findByText("ID")
        .next()
        .then(($elem) => {
            setDataSources(name, $elem.text());
        });

    switch (logSource) {
        case "Windows Events":
            cy.findByRole("dialog").within(() => {
                cy.findByText("Refresh Interval (seconds)")
                    .next()
                    .scrollIntoView({ ensureScrollable: false })
                    .should("contain.text", dataSource.refreshInterval);
            });
            break;
        case "Flat File":
            cy.findByRole("dialog").within(() => {
                cy.findByText("Format")
                    .next()
                    .scrollIntoView({ ensureScrollable: false })
                    .should("contain.text", dataSource.format);
                cy.findByText("Refresh Interval (minutes)")
                    .next()
                    .scrollIntoView({ ensureScrollable: false })
                    .should("contain.text", dataSource.refreshInterval);
            });
            break;
        case "Syslog":
            cy.findByRole("dialog").within(() => {
                cy.findByText("Format")
                    .next()
                    .scrollIntoView({ ensureScrollable: false })
                    .should("contain.text", dataSource.format);
                cy.findByText("Mode")
                    .next()
                    .scrollIntoView({ ensureScrollable: false })
                    .should("contain.text", dataSource.mode);
                cy.findByText("Port")
                    .next()
                    .scrollIntoView({ ensureScrollable: false })
                    .should("contain.text", dataSource.port);
            });
            break;
        case "TCP":
            cy.findByRole("dialog").within(() => {
                cy.findByText("Format")
                    .next()
                    .scrollIntoView({ ensureScrollable: false })
                    .should("contain.text", dataSource.format);
                cy.findByText("Port")
                    .next()
                    .scrollIntoView({ ensureScrollable: false })
                    .should("contain.text", dataSource.port);
            });
            break;
        default:
            break;
    }
    verifyDataQualitySummaryPageLabels(dataSource);
    cy.get("body").type("{esc}");
};

/**
 * Function to add a data feed tag at a specific index.
 *
 * @param {object} tag - The tag object containing key and value.
 * @param {number} index - The index where the tag needs to be added.
 */
const addDataSourceTag = (tag, index) => {
    cy.findByRole("button", { name: "Add" }).click();
    cy.get(`#config-input-key-field-${index}_text-field-metadata`).type(tag.key);
    cy.get(`#config-input-value-field-${index}_text-field-metadata`).type(tag.value);
};

/**
 * Checks if the data feed is compatible with the collector's operating system.
 *
 * @param {object} dataSource - The data feed object containing logSource information.
 * @param {string} collectorOs - The operating system of the collector.
 * @return {boolean} Returns true if the data feed is compatible with the collector's OS, false otherwise.
 */
const isCompatibleOS = (dataSource, collectorOs) =>
    !(dataSource.logSource === "Windows Events" && collectorOs !== "Windows");

/**
 * Configures inputs and tags based on the dataSource object.
 *
 * @param {object} dataSource - The data feed object containing logSource and port.
 */
const configureInputsAndTags = (dataSource) => {
    const isLinux = Cypress.env("os") === "Linux";
    switch (dataSource.logSource) {
        case "Syslog":
        case "TCP": {
            const { format, logSource, port } = dataSource;
            cy.findByLabelText("Format *").closest(".react-select__control").click();
            cy.findByRole("option", { name: format }).click({ force: true });
            cy.updateForm({ "Port *": port });
            if (logSource === "Syslog") {
                cy.findByLabelText("Mode *").closest(".react-select__control").click();
                cy.findByRole("option", { name: dataSource.mode }).click({ force: true });
            } else if (logSource === "TCP" && dataSource.enableTLS) {
                cy.setInput("Enable TLS", "true");
            }
            break;
        }
        case "Windows Events": {
            const { channels, query, refreshInterval, readHistoricalEvents } = dataSource;
            cy.findByLabelText("Refresh Interval (seconds) *").closest(".react-select__control").click();
            cy.findByRole("option", { name: refreshInterval }).click({ force: true });
            cy.setMultiSelectValues("config-input-channels", channels ?? []);
            if (readHistoricalEvents) {
                cy.get("[id*='config-input-read_historical_events']").check();
            }
            cy.findByText("Query")
                .next()
                .clear()
                .type(query ?? " ", { force: true });
            break;
        }
        case "Flat File": {
            const { format, refreshInterval, sourceFiles } = dataSource;

            cy.findByLabelText("Format *").closest(".react-select__control").click();
            cy.findByRole("option", { name: format }).click({ force: true });
            cy.findByLabelText("Refresh Interval (minutes) *").closest(".react-select__control").click();
            cy.findByRole("option", { name: refreshInterval }).click({ force: true });
            cy.setMultiSelectValues("config-input-source_paths", isLinux ? sourceFiles.linux : sourceFiles.windows);
            if (dataSource.exclusionFiles) {
                cy.setMultiSelectValues(
                    "config-input-exclude_paths",
                    isLinux ? dataSource.exclusionFiles.linux : dataSource.exclusionFiles.windows
                );
            }
            break;
        }
        default:
            break;
    }

    (dataSource.tags ?? []).forEach((tag, index) => {
        addDataSourceTag(tag, index);
    });
};

/**
 * Adds a data feed filter with the provided filter object at a specific index.
 *
 * @param {object} filter - The filter object containing key and value.
 * @param {number} index - The index where the filter needs to be added.
 */
const addDataSourceFilter = (filter, index) => {
    cy.findByRole("button", { name: "Add" }).click();
    cy.get(`#config-input-key-field-${index}_select-field-metadata`).type(filter.key).type("{enter}", { force: true });
    cy.get(`#config-input-value-field-${index}_text-field-metadata`).type(filter.value);
};

/**
 * Configures filters based on the dataSource object.
 *
 * @param {object} dataSource - The data feed object containing filters.
 */
const configureFilters = (dataSource) => {
    (dataSource.filters ?? []).forEach((filter, index) => {
        addDataSourceFilter(filter, index);
    });
};

/**
 * Retrieves data feeds based on the collector name and OS compatibility.
 *
 * @return {Array} Filtered data feeds based on OS compatibility.
 */
const getDataSources = () => {
    const dataSourcesPromise = Cypress.env("collectorName")
        ? cy.readFile("cypress/context/feeds.yml").then((yamlContent) => yaml.load(yamlContent))
        : cy.fixture("feeds.json").then((feeds) => feeds.data_collector);

    return dataSourcesPromise.then((result) =>
        Object.values(result.feeds).flatMap((sources) =>
            sources.filter((source) => isCompatibleOS(source, Cypress.env("os") ?? "Windows"))
        )
    );
};

/**
 * Retrieves or generates a data collector name for the data feed.
 *
 * @return {string} The data collector name.
 */
const getDataCollectorForDataSource = () => {
    if (Cypress.env("collectorName")) {
        return Cypress.env("collectorName");
    }
    gotoAddNewDataCollector();
    const randomString = Math.random().toString(36).substring(8);
    const collectorName = `data-collector-${randomString}`;
    cy.updateForm({
        "Collector Name *": collectorName,
        "OS *": "Windows",
    });
    cy.get("#configure-collector-btn").click();
    cy.findByLabelText("Close", { selector: "button" }).click();
    return collectorName;
};

const setDataSourceEnabled = (sourceName, enable = true) => {
    cy.findByText(sourceName)
        .parent()
        .within(($el) => {
            cy.wrap($el).click();
        });
    cy.findByRole("switch").should("be.enabled");
    cy.get(".react-switch-handle").click();
    cy.on("window:alert", () => true);
    switch (enable) {
        case false:
            cy.contains("Disable Data Feed");
            cy.findByRole("button", { name: "Yes" }).click();
            cy.contains("Disabling data feed").should("be.visible");
            break;
        case true:
            cy.contains("Enable Data Feed");
            cy.findByRole("button", { name: "Yes" }).click();
            cy.contains("Enabling data feed").should("be.visible");
            break;
        default:
            break;
    }
    cy.get("body").type("{esc}");
};

const setDataSourceEnabledInDialog = (sourceName, enable = true) => {
    cy.log(`Setting data source '${sourceName}' to ${enable ? "enabled" : "disabled"}`);

    cy.findByRole("dialog").within(() => {
        cy.log(`Clicking on the switch to ${enable ? "enable" : "disable"} the data feed`);
        cy.findByRole("switch").should("be.enabled");
        cy.get(".react-switch-handle").click();
    });

    cy.log("Waiting for the confirmation modal to appear");

    // Wait for the confirmation modal to appear and interact with it
    cy.get(".modal-content")
        .should("be.visible")
        .within(() => {
            if (!enable) {
                cy.log("Checking if the 'Disable Data Feed' button is visible");
                cy.contains("Disable Data Feed").should("be.visible");
            } else {
                cy.log("Checking if the 'Enable Data Feed' button is visible");
                cy.contains("Enable Data Feed").should("be.visible");
            }
            cy.findByRole("button", { name: "Yes" }).click();
        });

    // Wait for the status message to appear
    if (!enable) {
        cy.contains("Disabling data feed").should("be.visible");
        cy.log("Data feed is being disabled");
    } else {
        cy.contains("Enabling data feed").should("be.visible");
        cy.log("Data feed is being enabled");
    }

    cy.reload();
};

const validateFieldStateByLabelText = (labelTexts, state) => {
    labelTexts.forEach((labelText) => {
        cy.findByLabelText(labelText).should(state);
    });
};

const editS3DataSource = (sourceName, configType, formData = {}) => {
    const testrandomString = Math.random().toString(36).substring(7);
    const randomSourceName = `testSourceName${testrandomString}`;
    searchAndClickDataSource(sourceName);
    cy.findByTitle("Edit").click();

    switch (configType) {
        case "AWS S3": {
            Object.entries(formData).forEach(([key, value]) => {
                if (value === true) {
                    cy.setInput(key);
                } else {
                    cy.setInput(key, value);
                }
            });
            const S3bucket = Cypress.env("S3_BUCKET_NAME");
            cy.task("createBucket").then(() => {
                cy.findByLabelText("S3 Bucket Name *").clear().type(S3bucket);
            });
            cy.get('[id="reference-link"]').then((link) => {
                cy.request(link.prop("href")).its("status").should("eq", 200);
            });
            cy.findByRole("button", { name: "Next" }).click();
            cy.intercept("api/ingest/data-sources/**").as("getPolicy");
            cy.wait("@getPolicy");
            cy.findByText("Copy the IAM policy and KMS policy to apply to your AWS S3 bucket").should("be.visible");
            cy.get('[id="reference-link"]').then((link) => {
                cy.request(link.prop("href")).its("status").should("eq", 200);
            });
            cy.findByRole("button", { name: "Copy Content" }) // Select the button element
                .closest(".copyable-text-blob") // go to the wrapper div
                .find("pre") // find the <pre> tag inside it
                .invoke("text")
                .then((policyParams) => {
                    cy.task("putS3BucketPolicy", policyParams).then(() => {
                        cy.findByRole("button", { name: "Next" }).click();
                    });
                });

            cy.findByText("Setup AWS S3 bucket notification").should("be.visible");
            cy.get('[id="reference-link"]').then((link) => {
                cy.request(link.prop("href")).its("status").should("eq", 200);
            });
            cy.findByRole("button", { name: "Copy Content" }) // Select the button element
                .closest(".copyable-text-blob") // go to the wrapper div
                .find("pre") // find the <pre> tag inside it
                .invoke("text") // Get the text content of the div
                .then((QueueArnTxt) => {
                    const queueConfig = {
                        Id: `${S3bucket}-${randomSourceName}`,
                        Events: ["s3:ObjectCreated:*"],
                        QueueArn: QueueArnTxt,
                    };
                    cy.task("createS3EventNotification", queueConfig).then(() => {
                        cy.get('[class*="step done"]').should("have.length", 2);
                        cy.findByRole("button", { name: "Test Connection" }).click();
                        cy.validateToastMessage("Connection successful!");
                        cy.findByRole("button", { name: "Submit" }).click();
                    });
                });
            break;
        }
        default:
            break;
    }
};

const createNewDataSourceWithoutSubmitForm = (sourceName, configType, formData, authMethod = "Basic") => {
    const testrandomString = Math.random().toString(36).substring(7);
    const randomSourceName = `testSourceName${testrandomString}`;
    const randomOwnerName = `testOwnerName${testrandomString}`;
    const randomOwnerEmail = `testOwnerEmail${testrandomString}@bluvector.io`;

    cy.findByPlaceholderText("Search Data Feed Types").click().type(sourceName);
    cy.findByText(sourceName).click();
    cy.findByText(configType).click();

    cy.findByLabelText("Data Feed Name *").type(randomSourceName);
    cy.findByLabelText("Owner Name *").type(randomOwnerName);
    cy.findByLabelText("Owner E-mail *").type(randomOwnerEmail);

    switch (configType) {
        case "AWS S3": {
            Object.entries(formData).forEach(([key, value]) => {
                if (value === true) {
                    cy.setInput(key);
                } else {
                    cy.setInput(key, value);
                }
            });
            const S3bucket = Cypress.env("S3_BUCKET_NAME");

            cy.task("createBucket").then(() => {
                cy.findByLabelText("S3 Bucket Name *").type(S3bucket);
            });
            cy.get('[id="reference-link"]').then((link) => {
                cy.request(link.prop("href")).its("status").should("eq", 200);
            });
            cy.findByRole("button", { name: "Next" }).click();
            cy.intercept("api/ingest/data-sources/**").as("getPolicy");
            cy.wait("@getPolicy");
            cy.findByText("Copy the IAM policy and KMS policy to apply to your AWS S3 bucket").should("be.visible");
            cy.get('[id="reference-link"]').then((link) => {
                cy.request(link.prop("href")).its("status").should("eq", 200);
            });
            cy.findByRole("button", { name: "Copy Content" }) // Select the button element
                .closest(".copyable-text-blob") // go to the wrapper div
                .find("pre") // find the <pre> tag inside it
                .invoke("text") // Get the text content of the div
                .then((policyParams) => {
                    cy.task("putS3BucketPolicy", policyParams).then(() => {
                        cy.findByRole("button", { name: "Next" }).click();
                    });
                });

            cy.findByText("Setup AWS S3 bucket notification").should("be.visible");
            cy.get('[id="reference-link"]').then((link) => {
                cy.request(link.prop("href")).its("status").should("eq", 200);
            });
            cy.findByRole("button", { name: "Copy Content" }) // Select the button element
                .closest(".copyable-text-blob") // go to the wrapper div
                .find("pre") // find the <pre> tag inside it
                .invoke("text") // Get the text content of the div
                .then((QueueArnTxt) => {
                    const queueConfig = {
                        Id: `${S3bucket}-${randomSourceName}`,
                        Events: ["s3:ObjectCreated:*", "s3:ObjectRemoved:*"],
                        QueueArn: QueueArnTxt,
                    };
                    cy.task("createS3EventNotification", queueConfig).then(() => {
                        cy.get('[class*="step done"]').should("have.length", 2);
                    });
                });
            return {
                sourceName: randomSourceName,
                ownerName: randomOwnerName,
                ownerEmail: randomOwnerEmail,
                bucketName: S3bucket,
            };
        }
        case "API Ingest": {
            cy.findByRole("button", { name: "Next" }).click();

            switch (authMethod) {
                case "Bearer Token": {
                    cy.setReactSelectOption("[class*='react-select__input-container']", "Bearer Token");
                    cy.setInput("Token *", "testBearerToken");
                    break;
                }
                case "OAuth2": {
                    cy.setReactSelectOption("[class*='react-select__input-container']", "OAuth2");

                    const oauth2ConfigForm = {
                        "Refresh Token *": "testRefreshToken",
                        "Client Key *": "testClientKey",
                        "Client Secret *": "testClientSecret",
                        "Tenant Name *": "stg-test",
                        "Token URL *": "https://wd2-impl-services1.workday.com/ccx/oauth2/test_tenant/token",
                    };

                    cy.updateForm(oauth2ConfigForm);
                    break;
                }
                default: {
                    const basicAuthConfigForm = {
                        "Username *": Cypress.env("credentials").API_INGEST_BASIC.Username,
                        "Password *": Cypress.env("credentials").API_INGEST_BASIC.Password,
                        "API Base URL *": Cypress.env("credentials").API_INGEST_BASIC.BaseUrl,
                    };
                    cy.updateForm(basicAuthConfigForm);
                    break;
                }
            }

            return {
                sourceName: randomSourceName,
                ownerName: randomOwnerName,
                ownerEmail: randomOwnerEmail,
            };
        }
        default:
            break;
    }

    return {
        sourceName: randomSourceName,
        ownerName: randomOwnerName,
        ownerEmail: randomOwnerEmail,
    };
};

const verifyMetric = (label, pattern) => {
    cy.contains(".row", label)
        .should("be.visible")
        .find("p:not(.text-muted)")
        .should("be.visible")
        .invoke("text")
        .should("match", pattern);
};

const checkS3FileVisibility = (s3FileNames, shouldExist = true, attempt = 1, maxAttempts = 50, waitSecs = 3) => {
    cy.task("getFilesFromS3Bucket").then((files) => {
        const fileNames = files.map((file) => file.key);
        const filesPresent = s3FileNames.every((name) => fileNames.includes(name));

        if (shouldExist) {
            if (!filesPresent && attempt < maxAttempts) {
                cy.log(`Checking files ${s3FileNames.join(", ")} existence. Attempt ${attempt} of ${maxAttempts}`);
                return cy
                    .wait(waitSecs * 1000)
                    .then(() => checkS3FileVisibility(s3FileNames, shouldExist, attempt + 1, maxAttempts, waitSecs));
            }
            return cy
                .wrap(filesPresent)
                .should("be.true", `Files ${s3FileNames.join(", ")} not found after ${attempt} attempts`);
        }
        if (filesPresent && attempt < maxAttempts) {
            cy.log(`Checking files ${s3FileNames.join(", ")} non-existence. Attempt ${attempt} of ${maxAttempts}`);
            return cy
                .wait(waitSecs * 1000)
                .then(() => checkS3FileVisibility(s3FileNames, shouldExist, attempt + 1, maxAttempts, waitSecs));
        }
        return cy
            .wrap(filesPresent)
            .should("be.false", `Files ${s3FileNames.join(", ")} still present after ${attempt} attempts`);
    });
};

const verifyDataQualityActiveAlerts = () => {
    cy.get(".feed-health-alerts")
        .find(".alert-banner.error, .error-message, .alert.alert-danger.show")
        .should("not.exist");
    cy.findByRole("button", { name: "Edit Alert Settings" }).click();
    cy.get(".modal-content")
        .should("be.visible")
        .within(() => {
            cy.contains(".modal-title", "Data Quality - Alert Settings").should("be.visible");
            cy.get('input[type="checkbox"][role="switch"]').check({ force: true });
            cy.get("#new-data-check-hours").clear().type("48");
            cy.contains("button", "Save").click();
        });
    cy.validateToastMessage("Feed health alert settings successfully updated.");
    cy.findByRole("button", { name: "Edit Alert Settings" }).click();
    cy.get(".modal-content")
        .should("be.visible")
        .within(() => {
            cy.contains(".modal-title", "Data Quality - Alert Settings").should("be.visible");
            cy.get('input[type="checkbox"][role="switch"]').uncheck({ force: true });
            cy.contains("button", "Save").click();
            cy.findAllByRole("button", { name: "Close" }).should("be.visible").click();
        });
};

function assertLegendLabelsWithPolling(
    expectedTexts = ["Success (50.00%)", "Failed (>0.00%)", "Unmapped (50.00%)"],
    attempt = 1,
    maxAttempts = 50
) {
    cy.contains("Data Path").should("be.visible");
    // Check for zero bytes ingested in the data quality card
    cy.get(".data-quality-card").then(($card) => {
        // Find the Bytes Ingested row and check if it's 0 B
        const $bytesIngested = $card
            .find(".text-muted")
            .filter((i, el) => Cypress.$(el).text().includes("Bytes Ingested"))
            .parent()
            .next()
            .find("p");

        if ($bytesIngested.length && $bytesIngested.text().trim() === "0 B") {
            if (attempt < maxAttempts) {
                cy.log(`Bytes Ingested is 0 B, reloading... (attempt ${attempt + 1})`);
                cy.reload();
                cy.contains("Data Path").should("be.visible");
                assertLegendLabelsWithPolling(expectedTexts, attempt + 1, maxAttempts);
            } else {
                throw new Error("Bytes Ingested still 0 B after maximum polling attempts");
            }
        }
    });
    cy.then(() => {
        cy.reload();
        cy.contains("Data Path").should("be.visible");
        cy.get("#data-path-diagram-legend", { timeout: 10000 }).within(() => {
            cy.get(".data-path-category-label").then(($labels) => {
                const texts = $labels.map((i, el) => Cypress.$(el).text()).get();
                const allMatch = expectedTexts.every((val, idx) => texts[idx] === val);
                if (allMatch) {
                    expectedTexts.forEach((val, idx) => {
                        expect(texts[idx]).to.eq(val);
                    });
                } else if (attempt < maxAttempts) {
                    cy.log(`Legend labels not found, reloading... (attempt ${attempt + 1})`);
                    cy.reload();
                    assertLegendLabelsWithPolling(expectedTexts, attempt + 1, maxAttempts);
                } else {
                    throw new Error("Legend labels not found after polling");
                }
            });
        });
    });
}

/**
 * Waits for the application to confirm that no data feeds are present.
 *
 * This function continuously reloads the page and checks the following two conditions:
 *  1. The "0 Data Feed" metric is visible on the UI.
 *  2. The alert message "No Data Feeds configured" is displayed.
 *
 * The function uses a retry mechanism with a configurable maximum number of attempts and interval between retries.
 *
 * Dependencies:
 * - Requires `@testing-library/cypress` for `cy.findByText`
 *
 * @param {number} maxRetries - Maximum number of retries before failing the check (default: 30)
 * @param {number} interval - Time (in ms) to wait between retries (default: 3000ms)
 *
 * @example
 * waitForNoDataFeeds(); // Uses default retry and interval values
 * waitForNoDataFeeds(20, 2000); // Custom retry count and interval
 */
const waitForNoDataFeeds = (maxRetries = 20, interval = 3000) => {
    let retries = 0;

    function check() {
        cy.log(`🔁 Checking for '0 Data Feed' and alert message (Attempt ${retries + 1})`);

        cy.get("#ingest-page-sources").should("exist");

        cy.get("#ingest-data-metrics-overview li.list-inline-item")
            .should("exist")
            .invoke("text")
            .then((metricText) => {
                const hasNoFeeds = metricText.trim().includes("0 Data Feed");

                cy.get(".alert")
                    .should("exist")
                    .invoke("text")
                    .then((alertText) => {
                        const hasNoFeedsMessage = alertText.trim().includes("No Data Feeds configured");

                        if (hasNoFeeds && hasNoFeedsMessage) {
                            cy.log("✅ Verified: 0 Data Feed and 'No Data Feeds configured' message shown.");
                        } else if (retries < maxRetries) {
                            cy.log("❗ Conditions not yet met. Retrying...");
                            retries += 1;
                            cy.wait(interval * 1);
                            cy.reload().then(check);
                        } else {
                            throw new Error("❌ Max retries reached. '0 Data Feeds' conditions not satisfied.");
                        }
                    });
            });
    }

    check();
};

/**
 * Waits for the Data Feed count to decrease after a deletion.
 *
 * This function continuously reloads the page and checks if the visible Data Feed count
 * has reduced compared to the provided `initialCount`.
 *
 * It uses a retry mechanism with a configurable maximum number of attempts and interval between retries.
 * The function fails the test if the count does not decrease after all retry attempts.
 *
 * The count is extracted from the element:
 *   #ingest-data-metrics-overview li.list-inline-item
 * which should contain a string like "4 Data Feeds" or "1 Data Feed".
 *
 * @param {number} initialCount - The original count of Data Feeds before deletion.
 * @param {number} [maxRetries=10] - Maximum number of retries before failing the check.
 * @param {number} [intervalSeconds=3] - Time (in seconds) to wait between retries.
 *
 * @example
 * const initialCount = 4;
 * waitForDataFeedCountToUpdate(initialCount); // Uses default retry and interval
 *
 * @example
 * waitForDataFeedCountToUpdate(5, 20, 2); // Custom retry count and interval (in seconds)
 */
const waitForDataFeedCountToUpdate = (initialCount, maxRetries = 10, intervalSeconds = 3) => {
    let retries = 0;

    function waitForFeedToBeRemoved() {
        cy.get("#ingest-data-metrics-overview li.list-inline-item")
            .should("exist")
            .invoke("text")
            .then((text) => {
                const match = text.match(/(\d+)\s+Data\s+Feed[s]?/i);
                const currentCount = match ? parseInt(match[1], 10) : null;

                if (currentCount === null) {
                    throw new Error("Unable to parse Data Feed count.");
                }

                if (currentCount < initialCount) {
                    cy.log(`✅ Data Feed count reduced to ${currentCount}`);
                } else if (retries < maxRetries) {
                    retries += 1;
                    cy.log(`🔁 Count still ${currentCount}, retrying (${retries}/${maxRetries})...`);
                    cy.wait(intervalSeconds * 1000)
                        .then(() => cy.reload())
                        .then(() => cy.then(waitForFeedToBeRemoved));
                } else {
                    throw new Error(`❌ Data Feed count did not reduce after ${maxRetries} retries.`);
                }
            });
    }

    cy.then(waitForFeedToBeRemoved);
};

const waitForFeedStatusAndOpenDrawer = (sourceName, desiredStatus = "Healthy", maxReloads = 6, interval = 20000) => {
    let reloads = 0;

    function waitForStatus() {
        return cy
            .findByPlaceholderText("Search Data Feeds")
            .click({ force: true })
            .clear()
            .type(sourceName.includes("testSourceName") ? sourceName.split("testSourceName")[1] : sourceName)
            .then(() =>
                cy
                    .contains(".list-item", sourceName)
                    .find("[class^='status-indicator-state status-indicator-state-']")
                    .invoke("text")
                    .then((statusText) => {
                        const currentStatus = statusText.trim();
                        cy.log(`Current status for ${sourceName}: '${currentStatus}'`);
                        if (currentStatus !== desiredStatus && reloads < maxReloads) {
                            reloads += 1;
                            cy.log(`Status is '${currentStatus}', waiting for '${desiredStatus}' (attempt ${reloads})`);
                            // eslint-disable-next-line cypress/no-unnecessary-waiting -- waiting for backend state to update
                            cy.wait(interval);
                            cy.reload();
                            return waitForStatus();
                        }
                        if (currentStatus !== desiredStatus) {
                            throw new Error(
                                `Status for ${sourceName} did not become '${desiredStatus}' after ${maxReloads} reloads (last status: '${currentStatus}')`
                            );
                        }
                        cy.log(`Status for ${sourceName} is now '${desiredStatus}'`);
                        return null;
                    })
            );
    }

    return waitForStatus().then(() =>
        // Now click and open the drawer
        cy
            .findByText(sourceName)
            .parent()
            .click()
            .then(() =>
                // Wait for the drawer/modal to be visible
                cy.findByRole("dialog", { timeout: 10000 }).should("be.visible")
            )
    );
};

export {
    apiIngestFeedConfigHistory,
    assertLegendLabelsWithPolling,
    checkS3FileVisibility,
    configureFilters,
    configureInputsAndTags,
    createNewDataCollectorDataSource,
    createNewDataSource,
    createNewDataSourceWithoutSubmitForm,
    deleteDataSource,
    editS3DataSource,
    getDataCollectorForDataSource,
    getDataSources,
    gotoAddNewDataSourcePage,
    s3FeedConfigHistory,
    searchAndSelectSource,
    setDataSourceEnabled,
    setDataSourceEnabledInDialog,
    validateFieldStateByLabelText,
    validateNewDataSourceFlow,
    verifyDataQualityActiveAlerts,
    verifyDataQualityMetrics,
    verifyDataSourceCardDetails,
    verifyFeedConfigHistory,
    verifyMetric,
    verifyNewDataSourceWithDataCollector,
    waitForDataFeedCountToUpdate,
    waitForDataFeedToDelete,
    waitForElementToBeEnabled,
    waitForFeedStatusAndOpenDrawer,
    waitForNoDataFeeds,
    waitForStatusToBe,
};
