/// <reference types="cypress"/>
import { goToDataTab } from "../navigation_rbac/navigation_utils";

import {
    createNewDataSource,
    deleteDataSource,
    gotoAddNewDataSourcePage,
    searchAndSelectSource,
    verifyMetric,
    waitForDataFeedToDelete,
} from "./data_utils";

describe("Tenant: Data feed test connectivity tests", { tags: ["data", "full"] }, () => {
    let createdSources = [];
    const patterns = {
        bytesIngested: /^\d+(\.\d+)?\s*(B|MB|KB|GB|TB)$/,
        recordCount: /^\d+$/,
        percentage: /^\d+%$/,
        bandwidth: /^\d+(\.\d+)?\s*(B|kB|MB|GB|TB)$/,
        email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
        dateTime: /^([A-Za-z]+\s+\d{1,2}(?:st|nd|rd|th)\s+\d{4}\s+\d{1,2}:\d{2}\s*(?:am|pm)|--)$/i,
    };

    beforeEach(() => {
        cy.clearCookies();
        cy.visit(Cypress.env("credentials").DATABEE_UI);
    });

    afterEach(() => {
        cy.clickLogout();
    });

    after(() => {
        // delete all newly created data sources
        cy.visit(Cypress.env("credentials").DATABEE_UI);
        cy.manualLogin(
            Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
            Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
        );
        goToDataTab();
        if (createdSources.length > 0) {
            createdSources.forEach((src) => deleteDataSource(src));
            cy.wrap(createdSources).each((src) => {
                waitForDataFeedToDelete(src);
            });
            createdSources = [];
        }
    });

    it(
        "Admin User: Create Azure AD Data Feed | Check `Submit` should be disabled if connection fails.",
        { tags: "regression" },
        () => {
            cy.clearAllCookies();
            cy.manualLogin(
                Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
                Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
            );
            gotoAddNewDataSourcePage();

            // Intercept the API call for test connectivity and mock a failure response
            // This is to simulate a failed connection attempt
            cy.intercept("POST", "/api/ingest/data-sources/types/api-ingest/test-connectivity/", {
                statusCode: 500,
                body: {
                    detail: {
                        status: "fail",
                        message: "Connection failed",
                    },
                },
            }).as("mockTestConnectivityFailed");

            // Create a new data source with the name "Azure AD"
            // and set the type to "API Ingest"
            // and set the authentication type to "OAuth2"
            const { sourceName } = createNewDataSource(
                "Azure AD",
                "API Ingest",
                {
                    "Client Key *": "testClientKey",
                    "Client Secret *": "testClientSecret",
                    "Token URL *":
                        "https://login.microsoftonline.com/3adb963c-8e61-48e8-a06d-6dbb0dacea39/oauth2/v2.0/token",
                },
                "OAuth2",
                false
            );

            // Add the source name to the createdSources array to delete it later
            createdSources.push(sourceName);

            const testConnectivityButton = cy.get('button[title="Test Connection"]');

            // Submit button should be disabled before successful connection
            cy.findByRole("button", { name: "Submit" }).should("be.disabled");

            testConnectivityButton.click();

            // Wait for the alert with class "alert alert-danger" to be present
            cy.get("div.alert.alert-danger").should("exist");

            // Submit button should still be disabled if connection fails
            cy.findByRole("button", { name: "Submit" }).should("be.disabled");
        }
    );

    it(
        "Admin User: Create Azure AD Data Feed | Check `Submit` should be enabled if connection success.",
        { tags: "regression" },
        () => {
            cy.manualLogin(
                Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
                Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
            );
            gotoAddNewDataSourcePage();

            // Create a new data source with the name "Azure AD"
            // and set the type to "API Ingest"
            // and set the authentication type to "OAuth2"
            const { sourceName } = createNewDataSource(
                "Azure AD",
                "API Ingest",
                {
                    "Client Key *": "testClientKey",
                    "Client Secret *": "testClientSecret",
                    "Token URL *":
                        "https://login.microsoftonline.com/3adb963c-8e61-48e8-a06d-6dbb0dacea39/oauth2/v2.0/token",
                },
                "OAuth2",
                false
            );

            // Add the source name to the createdSources array to delete it later
            createdSources.push(sourceName);

            const testConnectivityButton = cy.get('button[title="Test Connection"]');
            const submitButton = cy.findByRole("button", { name: "Submit" });

            // Mock a successful connection response
            // This is to simulate a successful connection attempt
            cy.intercept("POST", "/api/ingest/data-sources/types/api-ingest/test-connectivity/", {
                statusCode: 200,
                body: {
                    success: true,
                    message: "Connection successful",
                },
            }).as("mockTestConnectivitySuccess");

            // Update the form with valid credentials
            cy.updateForm({
                "Client Key *": "testClientKeySuccess",
                "Client Secret *": "testClientSecretSuccess",
                "Token URL *": "https://login.microsoftonline.com/test-tenant/oauth2/v2.0/token",
            });

            testConnectivityButton.click();

            // Wait for the toast with message "Connection successful!" to be present
            cy.validateToastMessage("Connection successful!");

            // submitButton is enabled after successful connection
            submitButton.should("not.be.disabled");
            cy.findByRole("button", { name: "Submit" }).click();

            searchAndSelectSource(sourceName);

            // Verify Data Quality Summary Page
            cy.findByRole("button", { name: "View Data Quality Summary" }).click();
            cy.findByText("Data Quality").should("be.visible");
            cy.findByTitle("Data Feed Timeline").click();
            cy.contains(`Data Feed Timeline - ${sourceName}`);
            cy.findByRole("dialog").within(() => {
                cy.get(".alert-danger").should("not.exist");
            });
            cy.findByLabelText("Close").click();

            [
                { label: "Bytes Ingested", pattern: patterns.bytesIngested },
                { label: "Records Ingested", pattern: patterns.recordCount },
                { label: "Records Mapped", pattern: patterns.recordCount },
                { label: "Feed Bandwidth", pattern: patterns.bandwidth },
                { label: "Feed Mapping Efficiency", pattern: patterns.percentage },
                { label: "Data Last Ingested", pattern: patterns.dateTime },
                { label: "Owner", pattern: patterns.email },
            ].forEach(({ label, pattern }) => {
                verifyMetric(label, pattern);
            });

            cy.get("body").then(($body) => {
                if ($body.find("#data-path-diagram-legend").length) {
                    cy.get("#data-path-diagram-legend")
                        .should("be.visible")
                        .within(() => {
                            cy.contains("Success")
                                .parent()
                                .find(".data-path-category")
                                .should("have.css", "background-color", "rgb(17, 136, 47)");
                            cy.contains("Failed")
                                .parent()
                                .find(".data-path-category")
                                .should("have.css", "background-color", "rgb(236, 37, 13)");
                            cy.contains("Unmapped")
                                .parent()
                                .find(".data-path-category")
                                .should("have.css", "background-color", "rgb(8, 84, 142)");
                        });

                    cy.findByLabelText("Chart. Highcharts interactive chart.")
                        .should("be.visible")
                        .within(() => {
                            cy.get(".alert-danger").should("not.exist");
                        });
                } else {
                    cy.contains("No data ingested during the chosen time range").should("be.visible");
                }
            });
        }
    );
});
