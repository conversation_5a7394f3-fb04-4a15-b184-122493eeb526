/// <reference types="cypress"/>

import { goToDataTab } from "../navigation_rbac/navigation_utils";

import {
    assertLegendLabelsWithPolling,
    checkS3FileVisibility,
    createNewDataSource,
    deleteDataSource,
    editS3DataSource,
    gotoAddNewDataSourcePage,
    s3FeedConfigHistory,
    setDataSourceEnabledInDialog,
    verifyDataQualityMetrics,
    verifyDataSourceCardDetails,
    waitForDataFeedToDelete,
    waitForFeedStatusAndOpenDrawer,
} from "./data_utils";

const localFilePaths = ["cypress/fixtures/crowdstrike_source.json", "cypress/fixtures/aws_ingestion_sample.json"];
const s3FileNames = localFilePaths.map((filePath) => filePath.split("/").pop()); // Extract file names

describe(
    "Admin S3 Data Feed Management: Enable/Disable, Delete-After-Read, S3 File Handling, Metrics, and RBAC",
    { tags: ["data", "full"] },
    () => {
        before(() => {
            cy.log("Starting test setup: Admin S3 Data Feed Management");
            cy.loginSession(
                Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
                Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
            );
            gotoAddNewDataSourcePage();
        });

        after(() => {
            cy.clickLogout();
        });

        it("Admin can fully manage a CrowdStrike S3 data feed (enable/disable, toggle delete-after-read, verify S3, metrics, and RBAC)", () => {
            cy.log("Starting test: Admin can fully manage a CrowdStrike S3 data feed");
            // 1. Create data source
            const { sourceName, ownerName, ownerEmail, bucketName } = createNewDataSource("CrowdStrike", "AWS S3", {
                "Delete objects in the S3 bucket after read": true,
                "AWS Region *": "us-east-1",
                "Compression *": "none",
                "Content Type *": "JSON",
            });
            cy.log(`Created data source: ${sourceName}`);

            // 2. Verify initial state
            cy.log("Verifying initial state of the data source");
            waitForFeedStatusAndOpenDrawer(sourceName);
            verifyDataSourceCardDetails(
                ownerName,
                ownerEmail,
                "Healthy",
                Cypress.env("credentials").DATABEE_USER_ROLES.Admin,
                false
            );
            cy.log(`Data source ${sourceName} is in Healthy state`);

            // 3. Disable and re-enable
            cy.log("Disabling and re-enabling the data source");
            setDataSourceEnabledInDialog(sourceName, false);
            cy.log(`Data source ${sourceName} is now Disabled`);
            cy.log("Re-enabling the data source");
            waitForFeedStatusAndOpenDrawer(sourceName, "Disabled");
            setDataSourceEnabledInDialog(sourceName);
            waitForFeedStatusAndOpenDrawer(sourceName, "Healthy");
            cy.get("body").type("{esc}");
            cy.log(`Data source ${sourceName} has been re-enabled successfully`);

            // 4. Toggle delete-after-read and verify S3 file behavior
            // 4a. Upload files, should be deleted
            cy.log("Uploading files to S3 with delete-after-read enabled");
            cy.task("uploadFilesToS3Bucket", localFilePaths).then((result) => {
                expect(result.success).to.include.members(s3FileNames);
            });
            checkS3FileVisibility(s3FileNames, false);
            cy.log("S3 files should be deleted after read");

            // 4b. Disable delete-after-read, upload files, should remain
            cy.log("Disabling delete-after-read and uploading files, which should remain");
            editS3DataSource(sourceName, "AWS S3", { "Delete objects in the S3 bucket after read": false });
            waitForFeedStatusAndOpenDrawer(sourceName, "Healthy");
            cy.get("body").type("{esc}");
            cy.task("uploadFilesToS3Bucket", localFilePaths).then((result) => {
                expect(result.success).to.include.members(s3FileNames);
            });
            checkS3FileVisibility(s3FileNames, true);
            cy.log("S3 files should remain after upload with delete-after-read disabled");

            // 5. Verify config history and metrics
            cy.log("Verifying S3 feed configuration history and metrics");
            s3FeedConfigHistory(sourceName, ownerName, ownerEmail, bucketName);
            verifyDataQualityMetrics("10.76 kB", "1", "100%", "2", ownerEmail);
            cy.then(() => {
                assertLegendLabelsWithPolling();
            });
            // Assert main nodes in the diagram
            cy.get("svg.highcharts-root").within(() => {
                cy.contains(sourceName).should("exist");
                cy.contains("success").should("exist");
                cy.contains("unmapped").should("exist");
                cy.contains("config_state").should("exist");
                cy.contains("base_event").should("exist");
            });
            cy.log("Data feed metrics verified successfully");

            // 6. Cleanup
            cy.log("Cleaning up: Deleting S3 event notification and bucket policy");
            cy.task("deleteS3EventNotification", `${bucketName}-${sourceName}`);
            cy.task("deleteS3BucketPolicy");
            goToDataTab();
            deleteDataSource(sourceName);
            waitForDataFeedToDelete(sourceName);
            cy.log(`Data source ${sourceName} deleted successfully`);
        });
    }
);
