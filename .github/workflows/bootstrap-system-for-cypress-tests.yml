name: Feature-branch - bootstrap cluster & tenant

permissions:
  contents: write
  actions: write

on:
  workflow_dispatch:
    inputs:
      vcluster-name:
        description: VCluster Name
        required: true
        type: string
      profile:
        description: Profile to install databee components    
        required: true
        type: choice
        options:
          - stable
          - latest
          - light
          - test
          - demo
          - demo-live
        default: latest
      snowflake-database:
        description: 'Comma-separated Snowflake Database Names (e.g., STG_TEST_DATAFAKER_legacy,STG_TEST_DATAFAKER_edge)'
        required: true
        type: string
        default: 'STG_TEST_DATAFAKER_legacy,STG_TEST_DATAFAKER_edge'
      tenant-names:
        description: Comma-separated tenant names (leave empty for auto-generation)
        required: false
        type: string
  
  workflow_call:
    inputs:
      vcluster-name:
        description: VCluster Name
        required: true
        type: string
      profile:
        description: Profile to install databee components    
        required: true
        type: string
        default: latest
      snowflake-database:
        description: 'Comma-separated Snowflake Database Names (e.g., STG_TEST_DATAFAKER_legacy,STG_TEST_DATAFAKER_edge)'
        required: true
        type: string
        default: 'STG_TEST_DATAFAKER_legacy,STG_TEST_DATAFAKER_edge'
      tenant-names:
        description: Comma-separated tenant names (leave empty for auto-generation)
        required: false
        type: string

env:
  ACCOUNT: "databee-us-dev"
  REGION: "us-east-1"
  ENVIRONMENT: "databee-us-dev:us-east-1"
  CLUSTER_ADMIN_USERNAME: "<EMAIL>"
  SNOWFLAKE_DATABASE: "${{ inputs.snowflake-database }}"
  HOST_CLUSTER_NAME: "vcluster-us-dev"
  CLUSTER_NAME: "${{ inputs.vcluster-name }}"
  ARTIFACTORY_URL: partners.artifactory.comcast.com
  ARTIFACTORY_USERNAME: svc-databee-github
  ARTIFACTORY_PYPI_REPO: artifactory/api/pypi/databee-pypi/simple
  ARTIFACTORY_TOKEN: ${{ secrets.SVC_DATABEE_JFROG_TOKEN }}

jobs:
  check-vcluster:
    name: Check if Cluster exists
    runs-on: databee-runner
    outputs:
      IN_VCLUSTER: ${{ steps.set_in_vcluster.outputs.IN_VCLUSTER }}
      VCLUSTER_STATUS: ${{ steps.set_in_vcluster.outputs.STATUS }}
    steps:
      - name: Setup vCluster CLI
        uses: comcast-bluebird/actions/setup-vcluster@main
        with:
          version: "0.20.0"

      - name: Setup AWS
        id: creds
        uses: comcast-bluebird/actions/setup-aws@main
        with:
          account: 'databee-us-dev'
          region: 'us-east-1'
          cluster: 'vcluster-us-dev'
          kube-config-path: /root/.kube/config
          use-runner-role: true
          export-env: true

      - name: check if cluster exists
        id: set_in_vcluster
        env:
          CLUSTER_NAME: ${{ inputs.vcluster-name }}
        run: |
            echo "Checking if cluster '$CLUSTER_NAME' exists..."
              
            # Check vCluster list
            aws eks update-kubeconfig --name vcluster-us-dev
            VCLUSTER_OUTPUT=$(vcluster list)
            IN_VCLUSTER=$(echo "$VCLUSTER_OUTPUT" | grep -q "$CLUSTER_NAME" && echo "true" || echo "false")
            echo "IN_VCLUSTER=$IN_VCLUSTER"
            echo "IN_VCLUSTER=$IN_VCLUSTER" >> "$GITHUB_OUTPUT"
            STATUS=$(echo "$VCLUSTER_OUTPUT" | grep "$CLUSTER_NAME" | awk -F '|' '{gsub(/^[ \t]+|[ \t]+$/, "", $3); print $3}')
            echo "STATUS=$STATUS"
            echo "STATUS=$STATUS" >> "$GITHUB_OUTPUT"

      - uses: colpal/actions-clean@v1
        if: always()

  resume-vcluster:
    if: needs.check-vcluster.outputs.VCLUSTER_STATUS == 'Paused' && needs.check-vcluster.outputs.IN_VCLUSTER == 'true'
    uses: comcast-bluebird/vcluster-manager/.github/workflows/vcluster_resume.yaml@main
    needs: check-vcluster
    with:
      vcluster_name: ${{ inputs.vcluster-name }}
    secrets: inherit

  create-vcluster:
    needs: check-vcluster
    if: needs.check-vcluster.outputs.IN_VCLUSTER == 'false'
    name: Create VCluster
    uses: comcast-bluebird/databee-baseline/.github/workflows/deploy.yaml@main
    with:
      environment: "databee-us-dev:us-east-1"
      cluster: "${{ inputs.vcluster-name }}"
      profile: "${{ inputs.profile }}"
      vCluster: true
    secrets: inherit

  environment-configuration:
    name: Cypress-test Environment Configuration
    needs: create-vcluster
    runs-on: databee-runner
    environment: "databee-us-dev:us-east-1"
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          repository: comcast-bluebird/cypress-tests
          ref: main
          token: ${{ secrets.SVC_DATABEE_GITHUB_RW }}
      
      - name: Setup AWS
        id: creds
        uses: comcast-bluebird/actions/setup-aws@main
        with:
          account: ${{ env.ACCOUNT }}
          region: ${{ env.REGION }}
          cluster: ${{ env.HOST_CLUSTER_NAME }}
          kube-config-path: ".kube/${{ env.ACCOUNT }}/${{ env.HOST_CLUSTER_NAME }}"
          use-runner-role: true
          export-env: true

      - name: Setup vCluster CLI
        uses: comcast-bluebird/actions/setup-vcluster@main
      
      - name: Connect To Vcluster
        run: |
          kubectl get secret -n $CLUSTER_NAME vc-$CLUSTER_NAME -o=jsonpath='{.data.config}' --kubeconfig ".kube/$ACCOUNT/$HOST_CLUSTER_NAME" | base64 -d > .kube/$ACCOUNT/$CLUSTER_NAME
      
      - name: Create pip.conf
        run: |
          echo "[global]" >> pip.conf
          echo "index-url = https://${{ env.ARTIFACTORY_USERNAME }}:${{ env.ARTIFACTORY_TOKEN }}@${{ env.ARTIFACTORY_URL }}/${{ env.ARTIFACTORY_PYPI_REPO }}" >> pip.conf
      
      - name: Install requirements
        run: |
          PIP_CONFIG_FILE=pip.conf pip install --upgrade -r scripts/requirements.txt

      - name: Wait for namespace iris to be available
        shell: bash
        env:
          account: ${{ env.ACCOUNT }}
          region: ${{ env.REGION }}
          cluster: ${{ env.HOST_CLUSTER_NAME }}
          runner-access-key: ${{ secrets.SVC_CICD_ACCESS_KEY_ID }}
          runner-secret-key: ${{ secrets.SVC_CICD_SECRET_ACCESS_KEY }}
          AWS_ACCESS_KEY_ID: ${{ steps.creds.outputs.aws-access-key-id }}
          AWS_SECRET_ACCESS_KEY: ${{ steps.creds.outputs.aws-secret-access-key }}
        run: |
          SECONDS=0
          MAX_WAIT=600
          echo "Waiting for namespace iris to be available..."
          while true; do
            kubectl get namespace iris --request-timeout=5s --kubeconfig .kube/$ACCOUNT/$CLUSTER_NAME && break
            echo "Still waiting..."
            sleep 5
            if [ $SECONDS -ge $MAX_WAIT ]; then
              echo "Timeout reached. Exiting."
              break
            fi
          done
          echo "Namespace iris is now available."

      - name: Get Cluster Credentials
        shell: bash
        env:
          account: ${{ env.ACCOUNT }}
          region: ${{ env.REGION }}
          cluster: ${{ env.HOST_CLUSTER_NAME }}
          runner-access-key: ${{ secrets.SVC_CICD_ACCESS_KEY_ID }}
          runner-secret-key: ${{ secrets.SVC_CICD_SECRET_ACCESS_KEY }}
          AWS_ACCESS_KEY_ID: ${{ steps.creds.outputs.aws-access-key-id }}
          AWS_SECRET_ACCESS_KEY: ${{ steps.creds.outputs.aws-secret-access-key }}
        run: |
          SECONDS=0
          MAX_WAIT=600
          echo "Waiting for app secrets to be available..."
          while true; do
            kubectl --kubeconfig .kube/$ACCOUNT/$CLUSTER_NAME -n iris get secrets app-secrets -o jsonpath='{.data.initial_cluster_password}' && break
            echo "Still waiting..."
            sleep 5
            # Check for timeout
            if [ $SECONDS -ge $MAX_WAIT ]; then
              echo "Timeout reached. Exiting."
              exit 1  # Exit with an error status
            fi
          done
          CLUSTER_ADMIN_PASSWORD=$(kubectl --kubeconfig .kube/$ACCOUNT/$CLUSTER_NAME -n iris get secrets app-secrets -o jsonpath='{.data.initial_cluster_password}' | base64 -d)
          echo CLUSTER_ADMIN_PASSWORD=$CLUSTER_ADMIN_PASSWORD >> $GITHUB_ENV

      - name: Execute helper script
        run: |
          export KUBECONFIG=".kube/$ACCOUNT/$CLUSTER_NAME"
          
          if [ -z "${{ inputs.tenant-names }}" ]; then
            # Auto-generate tenant name if no input provided
            TENANT_NAME="cy-$(openssl rand -hex 2)"
            echo "Using auto-generated tenant name: $TENANT_NAME"
            export TENANT_NAME
            python3 scripts/configure_environment.py --tenant-name "$TENANT_NAME" --db-name "${SNOWFLAKE_DATABASE}"
            echo "Cypress Secret: cypress_test/${{ inputs.vcluster-name }}-$TENANT_NAME/env" >> $GITHUB_STEP_SUMMARY
          else
            IFS=',' read -ra TENANT_ARRAY <<< "${{ inputs.tenant-names }}"
            for tenant in "${TENANT_ARRAY[@]}"; do
              # Remove any whitespace
              TENANT_NAME=$(echo "$tenant" | xargs)
              echo "Processing tenant: $TENANT_NAME"
              export TENANT_NAME
              
              # Get the matching Snowflake database name based on tenant name
              DB_NAME=$(echo "${SNOWFLAKE_DATABASE}" | tr ',' '\n' | grep -i "${TENANT_NAME}" || echo "${SNOWFLAKE_DATABASE}")
              
              echo "Using database: ${DB_NAME}"
              python3 scripts/configure_environment.py --tenant-name "$TENANT_NAME" --db-name "${DB_NAME}"
              echo "Cypress Secret: cypress_test/${{ inputs.vcluster-name }}-$TENANT_NAME/env" >> $GITHUB_STEP_SUMMARY
            done
          fi
        env:
          ENVIRONMENT: "us-dev"
          KUBECONFIG: .kube/$ACCOUNT/$HOST_CLUSTER_NAME
        
  summary:
    name: Environment configuration
    runs-on: databee-runner
    needs: create-vcluster
    environment: "databee-us-dev:us-east-1"
    steps:
      - name: vCluster Details
        run: |
          echo "{{ env.GITHUB_RUN_ID }}"
          echo "## VCluster Details" >> $GITHUB_STEP_SUMMARY
          echo "Admin UI: https://${{ env.CLUSTER_NAME }}-admin.us-dev.databee.buzz/login" >> $GITHUB_STEP_SUMMARY
          echo "Admin Username: <EMAIL>" >> $GITHUB_STEP_SUMMARY
          echo "AWS ACCOUNT: ${{ env.ACCOUNT }}" >> $GITHUB_STEP_SUMMARY
