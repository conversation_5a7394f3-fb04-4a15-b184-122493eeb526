name: Feature-branch - destroy cluster & tenant

permissions:
  contents: write
  actions: write

on:
  workflow_dispatch:
    inputs:
      vcluster-name:
        description: VCluster Name
        required: true
        type: string
  
  workflow_call:
    inputs:
      vcluster-name:
        description: VCluster Name
        required: true
        type: string

env:
  ACCOUNT: "databee-us-dev"
  REGION: "us-east-1"
  ENVIRONMENT: "databee-us-dev:us-east-1"
  HOST_CLUSTER_NAME: "vcluster-us-dev"
  CLUSTER_NAME: "${{ inputs.vcluster-name }}"

jobs:
  destroy-vcluster:
    name: Destroy VCluster
    uses: comcast-bluebird/databee-baseline/.github/workflows/destroy.yaml@main
    with:
      environment: "databee-us-dev:us-east-1"
      cluster: "${{ inputs.vcluster-name }}"
      vCluster: true
    secrets: inherit
  
  delete-secret:
    name: Delete Cypress-test Secret
    runs-on: databee-runner
    needs: destroy-vcluster
    environment: "databee-us-dev:us-east-1"
    steps:
      - name: Setup AWS
        uses: comcast-bluebird/actions/setup-aws@main
        with:
          account: ${{ env.ACCOUNT }}
          region: ${{ env.REGION }}
          cluster: ${{ env.HOST_CLUSTER_NAME }}
          kube-config-path: ".kube/${{ env.ACCOUNT }}/${{ env.HOST_CLUSTER_NAME }}"
          use-runner-role: true
          export-env: true
      
      - name: Delete Cypress-test Secret
        run: |
          aws secretsmanager delete-secret --secret-id cypress_test/${{ inputs.vcluster-name }}/env --force-delete-without-recovery
