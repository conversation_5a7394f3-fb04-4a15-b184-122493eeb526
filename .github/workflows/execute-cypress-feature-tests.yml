name: Feature-branch - Execute Cypress-tests

permissions:
  contents: write
  actions: write

on:
  workflow_dispatch:
    inputs:
      vcluster-name:
        description: VCluster Name
        required: true
        type: string
      spec-file:
        description: Spec File (cypress/e2e/ui/**/**/*.js) - Not used for smoke tests
        type: string
        required: false
      grep-tag:
        description: 'Select Tags (comma-separated list)'
        required: true
        type: string
        default: 'none'
      test-tag:
        description: Test Type
        type: choice
        default: 'feature-test'
        options:
          - 'feature-test'
          - 'smoke-test'
      component:
        description: Run component tests
        type: string
        required: false
      node-version:
        description: Node.js version to use
        type: string
        required: false
        default: '22.14.0'
      component-path:
        description: Path to component repo (if running component tests)
        type: string
        required: false
        default: 'component'
      tenant-name:
        description: Tenant Name
        type: string
        required: false

  workflow_call:
    inputs:
      vcluster-name:
        description: VCluster Name
        required: true
        type: string
      spec-file:
        description: Spec File (cypress/e2e/ui/**/**/*.js) - Not used for smoke tests
        type: string
        required: false
      grep-tag:
        description: 'Select Tags (comma-separated list)'
        required: true
        type: string
        default: 'none'
      test-tag:
        description: Test Type
        type: string
        default: 'feature-test'
      component:
        description: Component Name
        type: string
        required: false
      node-version:
        description: Node.js version to use
        type: string
        required: false
        default: '22.14.0'
      component-path:
        description: Path to component repo (if running component tests)
        type: string
        required: false
        default: 'component'
      tenant-name:
        description: Tenant Name
        type: string
        required: false
    secrets:
      SVC_DATABEE_GITHUB_RW:
        required: true
      SVC_DATABEE_JFROG_TOKEN:
        required: true

env:
  ACCOUNT: "databee-us-dev"
  REGION: "us-east-1"
  TEST_TYPE: "SnowFlake"

jobs:
  cypress-tests:
    name: Run Cypress Tests
    runs-on: databee-runner
    defaults:
      run:
        working-directory: ${{ inputs.test-tag == 'smoke-test' && inputs.component-path || './tests' }}
    steps:
      - name: Checkout code (Default)
        uses: actions/checkout@v4
      
      - name: Set Environment Variables
        env:
          AWS_ACCOUNT: ${{ env.ACCOUNT }}
        working-directory: ${{ inputs.test-tag == 'smoke-test' && '' || './tests' }}
        run: |
          ls $GITHUB_WORKSPACE
          json_path=$GITHUB_WORKSPACE/.github/settings/accounts.json
          jq -e ".[\"$AWS_ACCOUNT\"]" "$json_path" || {
              echo "Unsupported account: $AWS_ACCOUNT"
              exit 1
          }
          cycloudfront=$(jq -r ".[\"$AWS_ACCOUNT\"].cypress.cloudfront" "$json_path")
          echo "CLOUDFRONT_URL=$cycloudfront" >> $GITHUB_ENV

          cybucket=$(jq -r ".[\"$AWS_ACCOUNT\"].cypress.s3bucket" "$json_path")
          echo "CYPRESS_REPORT_BUCKET=$cybucket" >> $GITHUB_ENV

      - name: Checkout component repo with submodules (smoke-test)
        if: ${{ inputs.test-tag == 'smoke-test' }}
        uses: actions/checkout@v4
        with:
          repository: comcast-bluebird/${{ inputs.component }}
          ref: main
          submodules: 'recursive'
          token: ${{ secrets.SVC_DATABEE_GITHUB_RW }}
          fetch-depth: 0
      
      - name: Initialize and update submodules
        run: |
          git submodule init && \
          git submodule update --recursive --remote || \
          git submodule update --init --recursive && \
          git submodule status

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: ${{ inputs.node-version }}

      - name: get role
        run: aws sts get-caller-identity
  
      - name: Setup AWS
        id: creds
        uses: comcast-bluebird/actions/setup-aws@main
        with:
          account: ${{ env.ACCOUNT }}
          region: ${{ env.REGION }}
          cluster: ${{ inputs.test-tag == 'smoke-test' && 'vcluster-us-dev' || '' }}
          kube-config-path: ${{ inputs.test-tag == 'smoke-test' && '/root/.kube/config' || '' }}
          use-runner-role: true

      - name: Setup yarn
        uses: comcast-bluebird/actions/setup-yarn@main

      - name: Install Dependencies for smoke test
        env:
          YARN_NPM_ALWAYS_AUTH: true
          YARN_NPM_AUTH_TOKEN: ${{ secrets.SVC_DATABEE_JFROG_TOKEN }} 
        working-directory: ${{ inputs.test-tag == 'smoke-test' && 'cypress-tests/tests'}}
        run: |
          yarn install
    
      - name: Install Dependencies
        env:
          YARN_NPM_ALWAYS_AUTH: true
          YARN_NPM_AUTH_TOKEN: ${{ secrets.SVC_DATABEE_JFROG_TOKEN }} 
        run: |
          yarn cache clean
          yarn install

      - name: Install Packages
        run: |
          apt-get update
          apt-get install xvfb -y
          apt-get install libgbm1 -y
          apt-get install libasound2 -y
          apt-get install default-jdk -y

      - name: Set up Chrome
        run: |
          wget -q -O - https://dl-ssl.google.com/linux/linux_signing_key.pub | apt-key add -
          echo "deb [arch=amd64] https://dl.google.com/linux/chrome/deb/ stable main" > /etc/apt/sources.list.d/google-chrome.list
          apt-get update
          apt-get install -y google-chrome-stable

      - name: Run Cypress Tests
        id: run-cypress
        continue-on-error: true
        env:
          CYPRESS_ENVIRONMENT: ${{ inputs.tenant-name && format('{0}-{1}', inputs.vcluster-name, inputs.tenant-name) || inputs.vcluster-name }}
          CYPRESS_AWS_REGION: ${{ env.REGION }}
          CYPRESS_grepTags: ${{ (inputs.grep-tag != 'none' && inputs.grep-tag) || '-e2e_setup+-e2e_tests+-e2e_cleanup+-data_faker+-demo_staging' }}
          AWS_REGION: ${{ env.REGION }}
          AWS_ACCESS_KEY_ID: ${{ steps.creds.outputs.aws-access-key-id }}
          AWS_SECRET_ACCESS_KEY: ${{ steps.creds.outputs.aws-secret-access-key }}
          AWS_SESSION_TOKEN: ${{ steps.creds.outputs.aws-session-token }}
          TEST_TAG: ${{ inputs.test-tag }}
          SPEC_FILE: ${{ inputs.spec-file }}
          GREP_TAG: ${{ inputs.grep-tag }}
        run: |
          export TEST_IN_WORKFLOW=true
          if [ "$TEST_TAG" != "smoke-test" ]; then
            
            CMD="npx cypress run --browser chrome"

            if [ -n "$SPEC_FILE" ] && [ "$TEST_TAG" != "smoke-test" ]; then
              CMD="$CMD --spec '$SPEC_FILE'"
            fi

            if [ -n "$GREP_TAG" ] && [ "$GREP_TAG" != "none" ]; then
              CMD="$CMD --env grepTags=$GREP_TAG"
            fi

            echo "Running: $CMD"
            eval $CMD
          else
            CMD="yarn cypress run --browser chrome"
            eval $CMD
          fi
          # Store the exit code to use later
          echo "cypress_exit_code=$?" >> $GITHUB_OUTPUT

      - name: Generate Report
        env:
          TEST_TAG: ${{ inputs.test-tag }}
        working-directory: component
        run: |
          if [ "$TEST_TAG" != "smoke-test" ]; then
            yarn gen:report
          else
            yarn allure generate allure-results --clean --single-file -o allure-report
          fi


      - name: Setup AWS (Refresh connection)
        if: always()
        id: creds-refresh
        uses: comcast-bluebird/actions/setup-aws@main
        with:
          account: ${{ env.ACCOUNT }}
          region: ${{ env.REGION }}
          use-runner-role: true

      - name: Upload Allure Report to S3
        env:
          AWS_REGION: ${{ env.REGION }}
          AWS_ACCESS_KEY_ID: ${{ steps.creds-refresh.outputs.aws-access-key-id }}
          AWS_SECRET_ACCESS_KEY: ${{ steps.creds-refresh.outputs.aws-secret-access-key }}
          AWS_SESSION_TOKEN: ${{ steps.creds-refresh.outputs.aws-session-token }}
          CYPRESS_REPORT_BUCKET: ${{ env.CYPRESS_REPORT_BUCKET }}
        run: |
          if [ -n "$CYPRESS_REPORT_BUCKET" ]; then
            aws s3 cp allure-report s3://$CYPRESS_REPORT_BUCKET/Feature-Test-Allure-Report-${{ github.run_number}} --recursive
          else
            echo "Skipping S3 upload as CYPRESS_REPORT_BUCKET is not set"
          fi

      - name: Add Report Link to Summary
        if: ${{ always() && !failure() && !cancelled() }}
        env:
          CLOUDFRONT_URL: ${{ env.CLOUDFRONT_URL }}
          RUN_NUMBER: ${{ github.run_number }}
        run: |
          if [ -n "$CLOUDFRONT_URL" ]; then
            {
              echo "### Allure Report"
              echo "[View Report]($CLOUDFRONT_URL/Feature-Test-Allure-Report-$RUN_NUMBER/index.html)"
            } >> $GITHUB_STEP_SUMMARY
          else
            echo "Skipping report link generation as CLOUDFRONT_URL is not set"
          fi
          
      - name: Fail workflow if tests failed
        if: steps.run-cypress.outputs.cypress_exit_code != '0'
        run: |
          echo "Cypress tests failed with exit code ${{ steps.run-cypress.outputs.cypress_exit_code }}"
          exit 1

      - name: Uninstall Yarn
        if: always()
        run: npm uninstall -g yarn

      - uses: colpal/actions-clean@v1
        if: always()