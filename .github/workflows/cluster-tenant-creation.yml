name: Configure Tenant Environment

permissions:
  contents: write
  actions: write

on:
  workflow_dispatch:
    inputs:
      vcluster-name:
        description: VCluster Name
        required: true
        type: string
      snowflake-database:
        description: 'Comma-separated Snowflake Database Names (e.g., STG_TEST_DATAFAKER_legacy,STG_TEST_DATAFAKER_edge)'
        required: true
        type: string
        default: 'STG_TEST_DATAFAKER_legacy,STG_TEST_DATAFAKER_edge'
      tenant-names:
        description: Comma-separated tenant names (leave empty for auto-generation)
        required: false
        type: string

env:
  ACCOUNT: "databee-us-dev"
  REGION: "us-east-1"
  ENVIRONMENT: "databee-us-dev:us-east-1"
  CLUSTER_ADMIN_USERNAME: "<EMAIL>"
  SNOWFLAKE_DATABASE: "${{ inputs.snowflake-database }}"
  HOST_CLUSTER_NAME: "vcluster-us-dev"
  CLUSTER_NAME: "${{ inputs.vcluster-name }}"
  ARTIFACTORY_URL: partners.artifactory.comcast.com
  ARTIFACTORY_USERNAME: svc-databee-github
  ARTIFACTORY_PYPI_REPO: artifactory/api/pypi/databee-pypi/simple
  ARTIFACTORY_TOKEN: ${{ secrets.SVC_DATABEE_JFROG_TOKEN }}

jobs:
  configure-tenant:
    name: Configure Tenant Environment
    runs-on: databee-runner
    environment: "databee-us-dev:us-east-1"
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          repository: comcast-bluebird/cypress-tests
          ref: feature/BLUEB-18436-updating-workflow-call
          token: ${{ secrets.SVC_DATABEE_GITHUB_RW }}
      
      - name: Setup AWS
        id: creds
        uses: comcast-bluebird/actions/setup-aws@main
        with:
          account: ${{ env.ACCOUNT }}
          region: ${{ env.REGION }}
          cluster: ${{ env.HOST_CLUSTER_NAME }}
          kube-config-path: ".kube/${{ env.ACCOUNT }}/${{ env.HOST_CLUSTER_NAME }}"
          use-runner-role: true
          export-env: true

      - name: Setup vCluster CLI
        uses: comcast-bluebird/actions/setup-vcluster@main
      
      - name: Connect To Vcluster
        run: |
          kubectl get secret -n $CLUSTER_NAME vc-$CLUSTER_NAME -o=jsonpath='{.data.config}' --kubeconfig ".kube/$ACCOUNT/$HOST_CLUSTER_NAME" | base64 -d > .kube/$ACCOUNT/$CLUSTER_NAME

      - name: Create pip.conf
        run: |
          echo "[global]" >> pip.conf
          echo "index-url = https://${{ env.ARTIFACTORY_USERNAME }}:${{ env.ARTIFACTORY_TOKEN }}@${{ env.ARTIFACTORY_URL }}/${{ env.ARTIFACTORY_PYPI_REPO }}" >> pip.conf
      
      - name: Install requirements
        run: |
          PIP_CONFIG_FILE=pip.conf pip install --upgrade -r scripts/requirements.txt

      - name: Get Cluster Credentials
        shell: bash
        env:
          account: ${{ env.ACCOUNT }}
          region: ${{ env.REGION }}
          cluster: ${{ env.HOST_CLUSTER_NAME }}
          runner-access-key: ${{ secrets.SVC_CICD_ACCESS_KEY_ID }}
          runner-secret-key: ${{ secrets.SVC_CICD_SECRET_ACCESS_KEY }}
          AWS_ACCESS_KEY_ID: ${{ steps.creds.outputs.aws-access-key-id }}
          AWS_SECRET_ACCESS_KEY: ${{ steps.creds.outputs.aws-secret-access-key }}
        run: |
          SECONDS=0
          MAX_WAIT=600
          echo "Waiting for app secrets to be available..."
          while true; do
            kubectl --kubeconfig .kube/$ACCOUNT/$CLUSTER_NAME -n iris get secrets app-secrets -o jsonpath='{.data.initial_cluster_password}' && break
            echo "Still waiting..."
            sleep 5
            # Check for timeout
            if [ $SECONDS -ge $MAX_WAIT ]; then
              echo "Timeout reached. Exiting."
              exit 1  # Exit with an error status
            fi
          done
          CLUSTER_ADMIN_PASSWORD=$(kubectl --kubeconfig .kube/$ACCOUNT/$CLUSTER_NAME -n iris get secrets app-secrets -o jsonpath='{.data.initial_cluster_password}' | base64 -d)
          echo CLUSTER_ADMIN_PASSWORD=$CLUSTER_ADMIN_PASSWORD >> $GITHUB_ENV
          
      - name: Execute Configuration Script
        run: |
          export KUBECONFIG=".kube/$ACCOUNT/$CLUSTER_NAME"
          
          if [ -z "${{ inputs.tenant-names }}" ]; then
            # Auto-generate tenant name if no input provided
            TENANT_NAME="cy-$(openssl rand -hex 2)"
            echo "Using auto-generated tenant name: $TENANT_NAME"
            export TENANT_NAME
            python3 scripts/configure_environment.py --tenant-name "$TENANT_NAME" --db-name "${SNOWFLAKE_DATABASE}"
            echo "Cypress Secret: cypress_test/${{ inputs.vcluster-name }}-$TENANT_NAME/env" >> $GITHUB_STEP_SUMMARY
          else
            IFS=',' read -ra TENANT_ARRAY <<< "${{ inputs.tenant-names }}"
            for tenant in "${TENANT_ARRAY[@]}"; do
              # Remove any whitespace
              TENANT_NAME=$(echo "$tenant" | xargs)
              echo "Processing tenant: $TENANT_NAME"
              export TENANT_NAME
              
              # Get the matching Snowflake database name based on tenant name
              DB_NAME=$(echo "${SNOWFLAKE_DATABASE}" | tr ',' '\n' | grep -i "${TENANT_NAME}" || echo "${SNOWFLAKE_DATABASE}")
              
              echo "Using database: ${DB_NAME}"
              python3 scripts/configure_environment.py --tenant-name "$TENANT_NAME" --db-name "${DB_NAME}"
              echo "Cypress Secret: cypress_test/${{ inputs.vcluster-name }}-$TENANT_NAME/env" >> $GITHUB_STEP_SUMMARY
            done
          fi
        env:
          ENVIRONMENT: "us-dev"
          KUBECONFIG: .kube/$ACCOUNT/$HOST_CLUSTER_NAME