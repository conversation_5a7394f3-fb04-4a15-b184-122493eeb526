name: Cypress Tests - Latest Profile

permissions:
  contents: write
  actions: write

on:
  workflow_dispatch:
    inputs:
      aws-account:
        description: AWS Account <PERSON><PERSON>
        required: true
        type: choice
        default: databee-us-stg-custom
        options:
          - databee-us-stg-custom
          - databee-us-dev
      aws-region:
        description: AWS Region
        required: true
        type: choice
        default: us-east-1
        options:
          - us-east-1
          - us-east-2
      grep-tag:
        description: Select Tag
        type: choice
        default: ''
        options:
          - ''
          - smoke
          - full
          - cluster_test
          - regression
          - realCluster
          - config_user
          - config_sso
          - dashboard
          - my_profile
          - notification
          - entity_view
          - compliance
          - entitlements
          - exposure
          - config
          - data
          - search
          - content_delivery
      test-type:
        description: Select Config
        type: choice
        default: SnowFlake
        options:
          - SnowFlake
          - DataFaker
      teams-alert:
        description: Teams Alert
        type: boolean
        default: true
#  schedule:
    # Daily pipeline run at 1:00 AM EST & 6:00 AM UTC, Monday through Friday
#    - cron: "0 6 * * 1-5"

env:
  NODE_VERSION: '20.16.0'

defaults:
  run:
    working-directory: ./tests

jobs:
  snowflake-cypress-tests:
    name: Run Snowflake Tests
    runs-on: databee-runner
    if: ${{ github.event.inputs.test-type == 'All' || github.event.inputs.test-type == 'SnowFlake' || github.event_name == 'schedule' }}
    env:
      REPORT_NAME: 'Stg-Latest-'
      CYPRESS_ENVIRONMENT: 'stg-latest-snowflake'

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: get role
        run: aws sts get-caller-identity
      
      - name: Setup AWS
        id: creds
        uses: comcast-bluebird/actions/setup-aws@main
        with:
          account: ${{ inputs.aws-account || 'databee-us-stg-custom' }}
          region: ${{ inputs.aws-region || 'us-east-1' }}
          use-runner-role: true
          export-env: true
      
      - name: Setup yarn
        uses: comcast-bluebird/actions/setup-yarn@main

      - name: Set Environment Variables
        env:
          AWS_ACCOUNT: ${{ inputs.aws-account || 'databee-us-stg-custom' }}
        run: |
          json_path=$GITHUB_WORKSPACE/.github/settings/accounts.json
          jq -e ".[\"$AWS_ACCOUNT\"]" "$json_path" || {
              echo "Unsupported account: $AWS_ACCOUNT"
              exit 1
          }
          cycloudfront=$(jq -r ".[\"$AWS_ACCOUNT\"].cypress.cloudfront" "$json_path")
          echo "CLOUDFRONT_URL=$cycloudfront" >> $GITHUB_ENV

          cybucket=$(jq -r ".[\"$AWS_ACCOUNT\"].cypress.s3bucket" "$json_path")
          echo "CYPRESS_REPORT_BUCKET=$cybucket" >> $GITHUB_ENV
          
          TEAMS_WEBHOOK_DRY_RUN_URL=$(aws ssm get-parameter --name /cypress/teams_webhook_dry_run_url |jq -r '.Parameter.Value')
          echo TEAMS_WEBHOOK_URL=$TEAMS_WEBHOOK_DRY_RUN_URL >> $GITHUB_ENV

      - name: Install Dependencies
        env:
          YARN_NPM_ALWAYS_AUTH: true
          YARN_NPM_AUTH_TOKEN: ${{ secrets.SVC_DATABEE_JFROG_TOKEN }}
        run: yarn install

      - name: Install Packages
        run: |
          apt-get update
          apt-get install xvfb -y
          apt-get install libgbm1 -y
          apt-get install libasound2 -y
          apt-get install default-jdk -y

      - name: Set up Chrome
        run: |
          wget -q -O - https://dl-ssl.google.com/linux/linux_signing_key.pub | apt-key add -
          echo "deb [arch=amd64] https://dl.google.com/linux/chrome/deb/ stable main" > /etc/apt/sources.list.d/google-chrome.list
          apt-get update
          apt-get install -y google-chrome-stable

      - name: Run Cypress Tests
        env:
          CYPRESS_ENVIRONMENT: ${{ env.CYPRESS_ENVIRONMENT }}
          CYPRESS_AWS_REGION: ${{ inputs.aws-region || 'us-east-1' }}
          CYPRESS_grepTags: ${{ inputs.grep-tag || '-e2e_setup+-e2e_tests+-e2e_cleanup+-data_faker+-demo_staging+-demo_tests' }}
          CYPRESS_TEST_TYPE: ${{ inputs.test-type }}
          AWS_ACCESS_KEY_ID: ${{ steps.creds.outputs.aws-access-key-id }}
          AWS_SECRET_ACCESS_KEY: ${{ steps.creds.outputs.aws-secret-access-key }}
          AWS_SESSION_TOKEN: ${{ steps.creds.outputs.aws-session-token }}
        run: |
          export TEST_IN_WORKFLOW=true
          yarn cypress:test

      - name: Setup AWS (Refresh connection)
        id: creds-refresh
        uses: comcast-bluebird/actions/setup-aws@main
        with:
          account: ${{ inputs.aws-account || 'databee-us-stg-custom' }}
          region: ${{ inputs.aws-region || 'us-east-1' }}
          use-runner-role: true
          export-env: true

      - name: Upload Allure Report to S3
        env:
          AWS_REGION: ${{ inputs.aws-region || 'us-east-1' }}
          AWS_ACCESS_KEY_ID: ${{ steps.creds-refresh.outputs.aws-access-key-id }}
          AWS_SECRET_ACCESS_KEY: ${{ steps.creds-refresh.outputs.aws-secret-access-key }}
          AWS_SESSION_TOKEN: ${{ steps.creds-refresh.outputs.aws-session-token }}
        run: |
          aws s3 cp allure-report s3://${{ env.CYPRESS_REPORT_BUCKET }}/Cypress-Test-Results/${{ env.CYPRESS_ENVIRONMENT }}/${{ env.REPORT_NAME }}Allure-Report-${{ github.run_number }} --recursive

      - name: Set Alert Channel
        if: ${{ inputs.teams-alert == true || github.event_name == 'schedule' }}
        run: |
          TEAMS_WEBHOOK_URL=$(aws ssm get-parameter --name /cypress/teams_webhook_url |jq -r '.Parameter.Value')
          echo TEAMS_WEBHOOK_URL=$TEAMS_WEBHOOK_URL >> $GITHUB_ENV

      - name: Send Teams Notification
        if: ${{ inputs.teams-alert == true || github.event_name == 'schedule' }}
        env:
          GITHUB_URL: ${{ github.event.repository.html_url }}
          GITHUB_RUN_ID: ${{ github.run_id }}
          GITHUB_RUN_NUMBER: ${{ github.run_number}}
          NOTIFICATION_CARD_TITLE: 'E2E Cypress Tests - Stg-Latest-SnowFlake'
          REPORT_NAME: 'Stg-Latest-'
        run: |
          yarn teams:report

      - name: Uninstall Yarn
        if: always()
        run: npm uninstall -g yarn

      - uses: colpal/actions-clean@v1
        if: always()

  data-faker-cypress-tests:
    name: Run Data Faker Tests
    runs-on: databee-runner
    if: ${{ github.event.inputs.test-type == 'All' || github.event.inputs.test-type == 'DataFaker' }}
    env:
      REPORT_NAME: 'Stg-Latest-Data-Faker-'
      CYPRESS_ENVIRONMENT: 'datafaker-latest'

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: get role
        run: aws sts get-caller-identity
      
      - name: Setup AWS
        id: creds
        uses: comcast-bluebird/actions/setup-aws@main
        with:
          account: ${{ inputs.aws-account || 'databee-us-stg-custom' }}
          region: ${{ inputs.aws-region || 'us-east-1' }}
          use-runner-role: true
          export-env: true

      - name: Setup yarn
        uses: comcast-bluebird/actions/setup-yarn@main

      - name: Set Environment Variables
        env:
          AWS_ACCOUNT: ${{ inputs.aws-account || 'databee-us-stg-custom' }}
        run: |
          json_path=$GITHUB_WORKSPACE/.github/settings/accounts.json
          jq -e ".[\"$AWS_ACCOUNT\"]" "$json_path" || {
              echo "Unsupported account: $AWS_ACCOUNT"
              exit 1
          }
          cycloudfront=$(jq -r ".[\"$AWS_ACCOUNT\"].cypress.cloudfront" "$json_path")
          echo "CLOUDFRONT_URL=$cycloudfront" >> $GITHUB_ENV

          cybucket=$(jq -r ".[\"$AWS_ACCOUNT\"].cypress.s3bucket" "$json_path")
          echo "CYPRESS_REPORT_BUCKET=$cybucket" >> $GITHUB_ENV
          
          TEAMS_WEBHOOK_DRY_RUN_URL=$(aws ssm get-parameter --name /cypress/teams_webhook_dry_run_url |jq -r '.Parameter.Value')
          echo TEAMS_WEBHOOK_URL=$TEAMS_WEBHOOK_DRY_RUN_URL >> $GITHUB_ENV

      - name: Install Dependencies
        env:
          YARN_NPM_ALWAYS_AUTH: true
          YARN_NPM_AUTH_TOKEN: ${{ secrets.SVC_DATABEE_JFROG_TOKEN }}
        run: yarn install

      - name: Install Packages
        run: |
          apt-get update
          apt-get install xvfb -y
          apt-get install libgbm1 -y
          apt-get install libasound2 -y
          apt-get install default-jdk -y

      - name: Set up Chrome
        run: |
          wget -q -O - https://dl-ssl.google.com/linux/linux_signing_key.pub | apt-key add -
          echo "deb [arch=amd64] https://dl.google.com/linux/chrome/deb/ stable main" > /etc/apt/sources.list.d/google-chrome.list
          apt-get update
          apt-get install -y google-chrome-stable

      - name: Run Cypress Tests
        env:
          CYPRESS_ENVIRONMENT: ${{ env.CYPRESS_ENVIRONMENT }}
          CYPRESS_AWS_REGION: ${{ inputs.aws-region || 'us-east-1' }}
          CYPRESS_grepTags: ${{ inputs.grep-tag || '-e2e_setup+-e2e_tests+-e2e_cleanup+-data_lake+-demo_staging+-demo_tests' }}
          CYPRESS_TEST_TYPE: ${{ inputs.test-type }}
          AWS_ACCESS_KEY_ID: ${{ steps.creds.outputs.aws-access-key-id }}
          AWS_SECRET_ACCESS_KEY: ${{ steps.creds.outputs.aws-secret-access-key }}
          AWS_SESSION_TOKEN: ${{ steps.creds.outputs.aws-session-token }}
        run: |
          export TEST_IN_WORKFLOW=true
          yarn cypress:test

      - name: Setup AWS (Refresh connection)
        id: creds-refresh
        uses: comcast-bluebird/actions/setup-aws@main
        with:
          account: ${{ inputs.aws-account || 'databee-us-stg-custom' }}
          region: ${{ inputs.aws-region || 'us-east-1' }}
          use-runner-role: true
          export-env: true

      - name: Upload Allure Report to S3
        env:
          AWS_REGION: ${{ inputs.aws-region || 'us-east-1' }}
          AWS_ACCESS_KEY_ID: ${{ steps.creds.outputs.aws-access-key-id }}
          AWS_SECRET_ACCESS_KEY: ${{ steps.creds.outputs.aws-secret-access-key }}
          AWS_SESSION_TOKEN: ${{ steps.creds.outputs.aws-session-token }}
        run: |
          aws s3 cp allure-report s3://${{ env.CYPRESS_REPORT_BUCKET }}/Cypress-Test-Results/${{ env.CYPRESS_ENVIRONMENT }}/${{ env.REPORT_NAME }}Allure-Report-${{ github.run_number }} --recursive

      - name: Set Alert Channel
        if: ${{ inputs.teams-alert == true || github.event_name == 'schedule' }}
        run: |
          TEAMS_WEBHOOK_URL=$(aws ssm get-parameter --name /cypress/teams_webhook_url |jq -r '.Parameter.Value')
          echo TEAMS_WEBHOOK_URL=$TEAMS_WEBHOOK_URL >> $GITHUB_ENV

      - name: Send Teams Notification
        if: ${{ inputs.teams-alert == true || github.event_name == 'schedule' }}
        env:
          GITHUB_URL: ${{ github.event.repository.html_url }}
          GITHUB_RUN_ID: ${{ github.run_id }}
          GITHUB_RUN_NUMBER: ${{ github.run_number}}
          NOTIFICATION_CARD_TITLE: 'E2E Cypress Tests - Stg-Latest-Data-Faker'
          REPORT_NAME: 'Stg-Latest-Data-Faker-'
        run: |
          yarn teams:report

      - name: Uninstall Yarn
        if: always()
        run: npm uninstall -g yarn

      - uses: colpal/actions-clean@v1
        if: always()
