name: Cypress Tests - Test Profile

permissions:
  contents: write
  actions: write

on:
  workflow_dispatch:
    inputs:
      aws-account:
        description: AWS Account Alias
        required: true
        type: choice
        default: databee-us-stg-custom
        options:
          - databee-us-stg-custom
          - databee-us-dev
      aws-region:
        description: AWS Region
        required: true
        type: choice
        default: us-east-1
        options:
          - us-east-1
          - us-east-2
      grep-tag:
        description: Select Tag
        type: choice
        default: ''
        options:
          - ''
          - smoke
          - full
          - cluster_test
          - regression
          - realCluster
          - config_user
          - config_sso
          - dashboard
          - my_profile
          - notification
          - entity_view
          - config
          - compliance
          - entitlements
          - exposure
          - data
          - search
          - content_delivery
          - data_faker
          - remediation_actions
          - empty-db
      test-type:
        description: Select Config
        type: choice
        default: SnowFlake
        options:
          - All
          - SnowFlake
          - DataFaker
          - Remediation
      teams-alert:
        description: Teams Alert
        type: boolean
        default: true
      test-tag:
        description: Test Type
        type: string
        required: false
        default: profile-test
      component-path:
        description: Path to component repo (if running component tests)
        type: string
        required: false
        default: 'component'
      component:
        description: Component Name
        type: string
        required: false
        default: ''
  workflow_call:
    inputs:
      aws-account:
        description: AWS Account Alias
        required: false
        type: string
        default: databee-us-stg-custom
      aws-region:
        description: AWS Region
        required: false
        type: string
        default: us-east-1
      grep-tag:
        description: Select Tag
        type: string
        required: false
        default: ''
      test-type:
        description: Select Config
        type: string
        required: false
        default: SnowFlake
      teams-alert:
        description: Teams Alert
        type: boolean
        required: false
        default: true
      test-tag:
        description: Test Type
        type: string
        required: false
        default: profile-test
      component-path:
        description: Path to component repo (if running component tests)
        type: string
        required: false
        default: 'component'
      component:
        description: Component Name
        type: string
        required: false
        default: ''

env:
  NODE_VERSION: '20.16.0'

jobs:
  cypress-tests:
    name: Run ${{ matrix.config }} Tests
    runs-on: databee-runner
    strategy:
      fail-fast: false
      max-parallel: 3
      matrix:
        config: [snowflake, datafaker, remediation]
        include:
          - config: snowflake
            environment: stg-test-snowflake
            report-prefix: Stg-Test-
            grep-tag: ${{ inputs.grep-tag || 'empty-db' }}
            seedData: true
            test-type: SnowFlake
          - config: datafaker
            environment: stg-test-datafaker
            report-prefix: Stg-Test-DataFaker-
            grep-tag: ${{ inputs.grep-tag || 'data_faker' }}
            seedData: false
            test-type: DataFaker
          - config: remediation
            environment: stg-test-cy-test-rem-act
            report-prefix: Stg-Test-Remediation-
            grep-tag: ${{ inputs.grep-tag || 'remediation_actions' }}
            seedData: false
            test-type: Remediation
    env:
      REPORT_NAME: ${{ matrix.report-prefix }}
      CYPRESS_ENVIRONMENT: ${{ matrix.environment }}
    steps:
      - name: Filter Matrix
        run: |
          if [ "${{ inputs.test-type }}" = "All" ]; then
            echo "Running all tests"
          elif [ "${{ inputs.test-type }}" = "${{ matrix.test-type }}" ]; then
            echo "Test type matches matrix configuration ${{ matrix.test-type }}"
          else
            echo "Test type '${{ inputs.test-type }}' does not match matrix test type '${{ matrix.test-type }}' - skipping"
            exit 1
          fi

      - name: Run Tests
        run: echo "Running tests for ${{ matrix.config }} in ${{ matrix.environment }}"
        
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
    
      - name: Setup AWS
        id: creds
        uses: comcast-bluebird/actions/setup-aws@main
        with:
          account: ${{ inputs.aws-account || 'databee-us-stg-custom' }}
          region: ${{ inputs.aws-region || 'us-east-1' }}
          use-runner-role: true
          export-env: true

      - name: Setup yarn
        uses: comcast-bluebird/actions/setup-yarn@main

      - name: Set Environment Variables
        env:
          AWS_ACCOUNT: ${{ inputs.aws-account || 'databee-us-stg-custom' }}
        working-directory: ${{ inputs.test-tag == 'component-test' && '' || 'tests' }}
        run: |
          json_path=$GITHUB_WORKSPACE/.github/settings/accounts.json
          jq -e ".[\"$AWS_ACCOUNT\"]" "$json_path" || {
              echo "Unsupported account: $AWS_ACCOUNT"
              exit 1
          }
          cycloudfront=$(jq -r ".[\"$AWS_ACCOUNT\"].cypress.cloudfront" "$json_path")
          echo "CLOUDFRONT_URL=$cycloudfront" >> $GITHUB_ENV

          cybucket=$(jq -r ".[\"$AWS_ACCOUNT\"].cypress.s3bucket" "$json_path")
          echo "CYPRESS_REPORT_BUCKET=$cybucket" >> $GITHUB_ENV
          
          TEAMS_WEBHOOK_DRY_RUN_URL=$(aws ssm get-parameter --name /cypress/teams_webhook_dry_run_url |jq -r '.Parameter.Value')
          echo TEAMS_WEBHOOK_URL=$TEAMS_WEBHOOK_DRY_RUN_URL >> $GITHUB_ENV

      - name: Checkout component repo with submodules (component-test)
        if: ${{ inputs.test-tag == 'component-test' }}
        uses: actions/checkout@v4
        with:
          repository: comcast-bluebird/${{ inputs.component }}
          ref: main
          submodules: 'recursive'
          token: ${{ secrets.SVC_DATABEE_GITHUB_RW }}
          fetch-depth: 0

      - name: Install Dependencies
        working-directory: ${{ inputs.test-tag == 'component-test' && 'cypress-tests/tests' || 'tests' }}
        env:
          YARN_NPM_ALWAYS_AUTH: true
          YARN_NPM_AUTH_TOKEN: ${{ secrets.SVC_DATABEE_JFROG_TOKEN }}
        run: |
          yarn install
    
      - name: Install Dependencies for component test
        if: ${{ inputs.test-tag == 'component-test' }}
        working-directory: ${{ inputs.component-path }}
        env:
          YARN_NPM_ALWAYS_AUTH: true
          YARN_NPM_AUTH_TOKEN: ${{ secrets.SVC_DATABEE_JFROG_TOKEN }} 
        run: |
          yarn cache clean
          yarn install

      - name: Install Packages
        working-directory: ${{ inputs.test-tag == 'component-test' && inputs.component-path || 'tests' }}
        run: |
          apt-get update
          apt-get install xvfb -y
          apt-get install libgbm1 -y
          apt-get install libasound2 -y
          apt-get install default-jdk -y

      - name: Set up Chrome
        working-directory: ${{ inputs.test-tag == 'component-test' && inputs.component-path || 'tests' }}
        run: |
          wget -q -O - https://dl-ssl.google.com/linux/linux_signing_key.pub | apt-key add -
          echo "deb [arch=amd64] https://dl.google.com/linux/chrome/deb/ stable main" > /etc/apt/sources.list.d/google-chrome.list
          apt-get update
          apt-get install -y google-chrome-stable

      - name: Run Cypress Tests
        working-directory: ${{ inputs.test-tag == 'component-test' && inputs.component-path || 'tests' }}
        id: run-cypress
        continue-on-error: true
        env:
          CYPRESS_ENVIRONMENT: ${{ matrix.environment }}
          CYPRESS_AWS_REGION: ${{ inputs.aws-region || 'us-east-1' }}
          CYPRESS_grepTags: ${{ matrix.grep-tag }}
          CYPRESS_TEST_TYPE: ${{ inputs.test-type }}
          AWS_REGION: ${{ inputs.aws-region || 'us-east-1' }}
          AWS_ACCESS_KEY_ID: ${{ steps.creds.outputs.aws-access-key-id }}
          AWS_SECRET_ACCESS_KEY: ${{ steps.creds.outputs.aws-secret-access-key }}
          AWS_SESSION_TOKEN: ${{ steps.creds.outputs.aws-session-token }}
          CYPRESS_seedData: ${{ matrix.seedData }}
        run: |
          pwd
          export TEST_IN_WORKFLOW=true
          CMD="yarn cypress run --browser chrome"
          eval $CMD
          # Store the exit code to use later
          echo "cypress_exit_code=$?" >> $GITHUB_OUTPUT

      - name: Generate Report
        env:
          TEST_TAG: ${{ inputs.test-tag }}
        working-directory: ${{ inputs.test-tag == 'component-test' && inputs.component-path || 'tests' }}
        run: |
          if [ "$TEST_TAG" != "component-test" ]; then
            yarn gen:report
          else
            yarn allure generate allure-results --clean --single-file -o allure-report
          fi

      - name: Setup AWS (Refresh connection)
        id: creds-refresh
        uses: comcast-bluebird/actions/setup-aws@main
        with:
          account: ${{ inputs.aws-account || 'databee-us-stg-custom' }}
          region: ${{ inputs.aws-region || 'us-east-1' }}
          use-runner-role: true
  
      - name: Upload Allure Report to S3
        working-directory: ${{ inputs.test-tag == 'component-test' && inputs.component-path || 'tests' }}
        env:
          AWS_REGION: ${{ inputs.aws-region || 'us-east-1' }}
          AWS_ACCESS_KEY_ID: ${{ steps.creds-refresh.outputs.aws-access-key-id }}
          AWS_SECRET_ACCESS_KEY: ${{ steps.creds-refresh.outputs.aws-secret-access-key }}
          AWS_SESSION_TOKEN: ${{ steps.creds-refresh.outputs.aws-session-token }}
          CYPRESS_REPORT_BUCKET: ${{ env.CYPRESS_REPORT_BUCKET }}
        run: |
          if [ -n "$CYPRESS_REPORT_BUCKET" ]; then
            aws s3 cp allure-report s3://${{ env.CYPRESS_REPORT_BUCKET }}/Cypress-Test-Results/${{ matrix.environment }}/${{ env.REPORT_NAME }}Allure-Report-${{ github.run_number }} --recursive
          else
            echo "Skipping S3 upload as CYPRESS_REPORT_BUCKET is not set"
          fi

      - name: Add Report Link to Summary
        if: ${{ always() && !failure() && !cancelled() }}
        env:
          CLOUDFRONT_URL: ${{ env.CLOUDFRONT_URL }}
          RUN_NUMBER: ${{ github.run_number }}
          CYPRESS_ENVIRONMENT: ${{ matrix.environment }}
        run: |
          if [ -n "$CLOUDFRONT_URL" ]; then
            {
              echo "### Allure Report"
              echo "[View Report]($CLOUDFRONT_URL/Cypress-Test-Results/${{ matrix.environment }}/${{ env.REPORT_NAME }}Allure-Report-$RUN_NUMBER/index.html)"
            } >> $GITHUB_STEP_SUMMARY
          else
            echo "Skipping report link generation as CLOUDFRONT_URL is not set"
          fi
      - name: Set Alert Channel
        if: ${{ inputs.teams-alert == true || github.event_name == 'schedule' }}
        run: echo "TEAMS_WEBHOOK_URL=${{ secrets.TEAMS_WEBHOOK_URL }}" >> $GITHUB_ENV

      - name: Send Teams Notification
        if: ${{ inputs.teams-alert == true || github.event_name == 'schedule' }}
        working-directory: ${{ inputs.test-tag == 'component-test' && inputs.component-path || 'tests' }}
        env:
          GITHUB_URL: ${{ github.event.repository.html_url }}
          GITHUB_RUN_ID: ${{ github.run_id }}
          GITHUB_RUN_NUMBER: ${{ github.run_number}}
          NOTIFICATION_CARD_TITLE: 'E2E Cypress Tests - Stg-Test-${{ matrix.config }}'
          CYPRESS_ENVIRONMENT: ${{ matrix.environment }}
        run: |
          # Try to locate apiApp.js
          if [ -f "../cypress-tests/tests/apiApp.js" ]; then
            yarn node ../cypress-tests/tests/apiApp.js
            echo "path is ../cypress-tests/tests/apiApp.js"
          else
            echo "Error: Could not find apiApp.js in any expected location"
            echo "Current directory structure:"
            find . -name "apiApp.js"
            exit 1
          fi

      - name: Fail workflow if tests failed
        if: ${{ steps.run-cypress.outputs.cypress_exit_code == '' || steps.run-cypress.outputs.cypress_exit_code != '0' }}
        run: |
          echo "Cypress tests failed with exit code ${{ steps.run-cypress.outputs.cypress_exit_code }}"
          exit 1

      - name: Uninstall Yarn
        if: always()
        run: npm uninstall -g yarn

      - uses: colpal/actions-clean@v1
        if: always()
