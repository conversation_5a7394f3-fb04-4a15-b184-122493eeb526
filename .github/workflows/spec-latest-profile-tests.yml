name: Development Preview - Spec Run

permissions:
  contents: write
  actions: write

on:
  workflow_dispatch:
    inputs:
      aws-account:
        description: AWS Account <PERSON><PERSON>
        required: true
        type: choice
        default: databee-us-stg-custom
        options:
          - databee-us-stg-custom
          - databee-us-dev
          - databee-management
      aws-region:
        description: AWS Region
        required: true
        type: choice
        default: us-east-1
        options:
          - us-east-1
          - us-east-2
      spec-file:
        description: Spec File (cypress/e2e/ui/**/**/*.js)
        required: true
        type: string
      env-secret:
        description: Environment Secrets
        type: choice
        default: stg-latest-snowflake
        options:
          - stg-latest-snowflake
          - stg-latest-databricks
          - stg-test-databricks
          - stg-test-snowflake
          - stg-test-datafaker
          - demo-staging
          - None
      env-secret-custom:
        description: Custom Environment Secrets
        type: string
        required: false
      test-type:
        description: Select Config
        type: choice
        default: SnowFlake
        options:
          - SnowFlake
          - DataFaker
      teams-alert:
        description: Teams Alert
        type: boolean
        default: false

env:
  NODE_VERSION: '20.16.0'
  TEAMS_WEBHOOK_URL: ${{ secrets.TEAMS_WEBHOOK_DRY_RUN_URL }}

defaults:
  run:
    working-directory: ./tests

jobs:
  spec-cypress-tests:
    name: Run Cypress Spec Tests
    runs-on: databee-runner
    env:
      REPORT_NAME: '${{ inputs.test-type }}'

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
        
      - name: get role
        run: aws sts get-caller-identity

      - name: Setup AWS
        id: creds
        uses: comcast-bluebird/actions/setup-aws@main
        with:
          account: ${{ inputs.aws-account }}
          region: ${{ inputs.aws-region }}
          use-runner-role: true
          export-env: true

      - name: get role
        run: aws sts get-caller-identity

      - name: Setup yarn
        uses: comcast-bluebird/actions/setup-yarn@main

      - name: Set Environment Variables
        env:
          AWS_ACCOUNT: ${{ inputs.aws-account || 'databee-management' }}
        run: |
          json_path=$GITHUB_WORKSPACE/.github/settings/accounts.json
          jq -e ".[\"$AWS_ACCOUNT\"]" "$json_path" || {
              echo "Unsupported account: $AWS_ACCOUNT"
              exit 1
          }
          cycloudfront=$(jq -r ".[\"$AWS_ACCOUNT\"].cypress.cloudfront" "$json_path")
          echo "CLOUDFRONT_URL=$cycloudfront" >> $GITHUB_ENV

          cybucket=$(jq -r ".[\"$AWS_ACCOUNT\"].cypress.s3bucket" "$json_path")
          echo "CYPRESS_REPORT_BUCKET=$cybucket" >> $GITHUB_ENV

      - name: Install Dependencies
        env:
          YARN_NPM_ALWAYS_AUTH: true
          YARN_NPM_AUTH_TOKEN: ${{ secrets.SVC_DATABEE_JFROG_TOKEN }}
        run: yarn install

      - name: Install Packages
        run: |
          apt-get update
          apt-get install xvfb -y
          apt-get install libgbm1 -y
          apt-get install libasound2 -y
          apt-get install default-jdk -y

      - name: Set up Chrome
        run: |
          wget -q -O - https://dl-ssl.google.com/linux/linux_signing_key.pub | apt-key add -
          echo "deb [arch=amd64] https://dl.google.com/linux/chrome/deb/ stable main" > /etc/apt/sources.list.d/google-chrome.list
          apt-get update
          apt-get install -y google-chrome-stable

      - name: Ensure Custom AWS Secret Name is Provided
        if: inputs.env-secret == 'None'
        run: |
          if [[ -z "${{ inputs.env-secret-custom }}" ]]; then
            echo "Error: Custom Environment Secrets is required when Environment Secrets is None"
            exit 1
          fi

      - name: Run Cypress Tests
        env:
          CYPRESS_ENVIRONMENT: ${{ inputs.env-secret == 'None' && inputs.env-secret-custom || inputs.env-secret }}
          CYPRESS_AWS_REGION: ${{ inputs.aws-region || 'us-east-1' }}
          CYPRESS_grepTags: '-e2e_setup+-e2e_tests+-e2e_cleanup'
          CYPRESS_TEST_TYPE: ${{ inputs.test-type }}
          use-runner-role: true
          export-env: true
        run: |
          export TEST_IN_WORKFLOW=true
          npx cypress run --browser chrome --spec ${{ inputs.spec-file }} --config-file cypress.config.shared.js

      - name: Generate Report
        if: always()
        run: |
          yarn gen:report

      - name: Setup AWS (Refresh connection)
        if: always()
        id: creds-refresh
        uses: comcast-bluebird/actions/setup-aws@main
        with:
          account: ${{ inputs.aws-account }}
          region: ${{ inputs.aws-region }}
          use-runner-role: true
          export-env: true

      - name: Upload Allure Report to S3
        if: always()
        env:
          AWS_REGION: ${{ inputs.aws-region || 'us-east-1' }}
          AWS_ACCESS_KEY_ID: ${{ steps.creds-refresh.outputs.aws-access-key-id }}
          AWS_SECRET_ACCESS_KEY: ${{ steps.creds-refresh.outputs.aws-secret-access-key }}
          AWS_SESSION_TOKEN: ${{ steps.creds-refresh.outputs.aws-session-token }}
        run: aws s3 cp allure-report s3://${{ env.CYPRESS_REPORT_BUCKET }}/Dev-Prev-Allure-Report-${{ github.run_number}} --recursive

      - name: Set Alert Channel
        if: ${{ inputs.teams-alert == true }}
        run: echo "TEAMS_WEBHOOK_URL=${{ secrets.TEAMS_WEBHOOK_URL }}" >> $GITHUB_ENV

      - name: Send Teams Notification
        if: ${{ inputs.teams-alert == true }}
        env:
          GITHUB_URL: ${{ github.event.repository.html_url }}
          GITHUB_RUN_ID: ${{ github.run_id }}
          GITHUB_RUN_NUMBER: ${{ github.run_number}}
          NOTIFICATION_CARD_TITLE: 'E2E Cypress Tests - ${{ inputs.test-type }}'
          REPORT_NAME: '${{ inputs.test-type }}'
        run: |
          yarn teams:report

      - name: Add Report Link to Summary
        if: always()
        run: |
          {
            echo "### Allure Report"
            echo "[View Report](${{ env.CLOUDFRONT_URL }}/Dev-Prev-Allure-Report-${{ github.run_number}}/index.html)"
          } >> $GITHUB_STEP_SUMMARY

      - name: Uninstall Yarn
        if: always()
        run: npm uninstall -g yarn

      - uses: colpal/actions-clean@v1
        if: always()