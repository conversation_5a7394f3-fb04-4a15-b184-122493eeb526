import os
import json
import logging
import time
import socket
import sys
import argparse
from automation_sdk.utils.cluster_service import ClusterService
from automation_sdk.utils.api_service import ApiService
from automation_sdk.utils.kube_client import KubeClient
from automation_sdk.utils.aws_client import AWSClient
from automation_sdk.utils.tenant_service import tenantService
from constants import PLACEHOLDERS, replace_part


# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s', handlers=[logging.StreamHandler(sys.stdout)])
logger = logging.getLogger(__name__)

MAX_WAIT_TIME = 1200
CYPRESS_INITIAL_SECRET = "cypress_tests/feature-testing/env"

def create_tenant(cluster_service_client: ClusterService):
    try:
        # Validate required environment variables
        required_env_vars = ["TENANT_NAME", "GITHUB_ACTOR", "ENVIRONMENT", "CLUSTER_NAME"]
        for var in required_env_vars:
            if var not in os.environ:
                raise ValueError(f"Required environment variable {var} is not set")

        tenant_payload = {
            "name": os.environ["TENANT_NAME"],
            "short_name": os.environ["TENANT_NAME"],
            "on_trial": False,
            "active": True,
            "primary_contact_first_name": os.environ["GITHUB_ACTOR"],
            "primary_contact_last_name": os.environ["GITHUB_ACTOR"],
            "primary_email": f"{os.environ['GITHUB_ACTOR'].removesuffix('_comcast')}@cable.comcast.com",
            "entitlements": {
                "beekeeper": {"title": "BeeKeeper", "is_entitled": True, "entitlement_meta": None},
                "security_threats": {"title": "Security Threats", "is_entitled": True, "entitlement_meta": None},
                "security_hygiene": {"title": "Security Hygiene", "is_entitled": True, "entitlement_meta": None}
            }
        }
        tenant_creation_response = cluster_service_client.create_tenant(tenant_payload)
        if tenant_creation_response and "id" in tenant_creation_response:
            tenant_id = tenant_creation_response["id"]
            logger.info(f"Tenant created successfully. Tenant ID: {tenant_id}")
            return tenant_id
        else:
            logger.error("Tenant creation failed. Response: %s", json.dumps(tenant_creation_response, indent=2))
            return None

    except ValueError as ve:
        logger.error(f"Value Error: {str(ve)}")
    except json.JSONDecodeError as je:
        logger.error(f"JSON Decode Error: {str(je)}")
    except Exception as e:
        logger.exception(f"Unexpected error occurred: {str(e)}")
    return None


def wait_for_tenant_creation(api_service_client: ApiService):
    wait_time = 0
    login_payload = {
        "username": os.environ["CLUSTER_ADMIN_USERNAME"],
        "password": os.environ["CLUSTER_ADMIN_PASSWORD"],
    }

    while wait_time < MAX_WAIT_TIME:
        wait_time += 25
        try:
            response = api_service_client.login(login_payload)
            if response:
                logger.info("Login successful")
                return True
        except Exception as e:
            logger.info(f"Login failed. Retrying in 25 seconds. Error: {str(e)}")
        
        time.sleep(25)

    return False

def check_dns_resolution(domain):
    """Check if DNS has propagated for the given domain."""
    try:
        ip_addr = socket.gethostbyname(domain)
        logger.info(f"DNS resolved for {domain} at {ip_addr}")
        return True  # DNS resolved
    except socket.gaierror:
        logger.info(f"DNS resolution failed for {domain}")
        return False  # DNS not available yet


def wait_for_dns_resolution(domain):
    """Wait for DNS resolution to complete for the given domain."""
    start_time = time.time()
    while not check_dns_resolution(domain):
        if time.time() - start_time > MAX_WAIT_TIME:
            logger.error(f"DNS resolution timed out for {domain}")
            return False
        time.sleep(30)
    return True


def wait_for_pod(kube_client, namespace, pod_name_prefix):
    logger.info(f"Waiting for pod with prefix '{pod_name_prefix}' to be ready...")
    start_time = time.time()
    while time.time() - start_time < MAX_WAIT_TIME:
        pods = kube_client.get_pods_in_namespace(pod_name_prefix, False, namespace)
        if pods and pods[0].status.phase == 'Running':
            logger.info(f"Pod {pods[0].metadata.name} is ready")
            return pods[0]
        logger.info(f"Waiting for pod to be ready. Current status: {pods[0].status.phase if pods else 'Not found'}")
        time.sleep(10)
    logger.error(f"Timeout waiting for pod with prefix '{pod_name_prefix}' to be ready")
    return None


if __name__ == "__main__":
    try:
        parser = argparse.ArgumentParser(description='Configure environment for cypress tests')
        parser.add_argument('--tenant-name', required=True, help='Name of the tenant to create')
        parser.add_argument('--db-name', required=True, help='Name of the database to create')
        args = parser.parse_args()

        # Set tenant name in environment variables
        os.environ["TENANT_NAME"] = args.tenant_name
        os.environ["SNOWFLAKE_DATABASE"] = args.db_name
        logger.info(f"Configuring environment for tenant: {os.environ['TENANT_NAME']} with database: {os.environ['SNOWFLAKE_DATABASE']}")
        logger.info("Initializing clients")
        kube_client = KubeClient()
        aws_client = AWSClient()
        

        cluster_domain = f"{os.environ['CLUSTER_NAME']}-admin.{os.environ['ENVIRONMENT']}.databee.buzz"
        logger.info(f"Waiting for DNS resolution for {cluster_domain}")
        if not wait_for_dns_resolution(cluster_domain):
            raise TimeoutError(f"DNS resolution timed out for {cluster_domain}")
        
        api_service_for_cluster = ApiService(base_url=f"https://{cluster_domain}", isCluster=True)
        
        logger.info("Waiting for cluster admin UI to be up and running")
        if not wait_for_tenant_creation(api_service_for_cluster):
            raise TimeoutError("Cluster admin UI is not accessible.")
        cluster_service_client = ClusterService()

        logger.info("Wait a minute before starting tenant creation process")
        time.sleep(60) # Wait as some services are still not up and give 500 error
        logger.info("Waiting for pod to be ready")
        pod = wait_for_pod(kube_client, "iris", "databee-api-")
        if not pod:
            raise TimeoutError("Pod creation process timed out")
        logger.info("Starting tenant creation process")
        tenant_id = create_tenant(cluster_service_client)
        if not tenant_id:
            raise RuntimeError("Tenant creation failed")
        os.environ["TENANT_ID"] = str(tenant_id)

        tenant_domain = f"{os.environ['TENANT_NAME']}.{os.environ['ENVIRONMENT']}.databee.buzz"
        logger.info(f"Waiting for DNS resolution for {tenant_domain}")
        if not wait_for_dns_resolution(tenant_domain):
            raise TimeoutError(f"DNS resolution timed out for {tenant_domain}")
        
        api_service_client = ApiService()

        logger.info("Waiting for tenant to be up and running")
        if not wait_for_tenant_creation(api_service_client):
            raise TimeoutError("Tenant creation process timed out")
        
        logger.info("Fetching sample cypress secrets from AWS secrets manager")
        cypress_secrets = aws_client.get_secret(CYPRESS_INITIAL_SECRET)
        cypress_secrets = json.dumps(cypress_secrets, indent=2)
        for key, value in PLACEHOLDERS.items():
            cypress_secrets = replace_part(cypress_secrets, key, os.environ[value])
        cypress_secrets = json.loads(cypress_secrets)
        
        logger.info("Uploading prepared cypress secrets to AWS secrets manager")
        cypress_secret_name = f"cypress_test/{os.environ['CLUSTER_NAME']}-{os.environ['TENANT_NAME']}/env"
        if not aws_client.create_secret(cypress_secret_name, cypress_secrets):
            raise RuntimeError("Failed to upload cypress secrets to AWS secrets manager")
        logger.info(f"Successfully uploaded {cypress_secret_name} cypress secrets to AWS secrets manager")

        tenant_service_client = tenantService()
        cypress_secrets = json.loads(cypress_secrets)
        
        logger.info("Configuring snowflake datalake in tenant!")
        snowflake_creds = cypress_secrets["SNOWFLAKE_CREDENTIALS"]
        account_identifier, username, password, role, warehouse, database, private_key = snowflake_creds.values()
        tenant_name = os.environ['TENANT_NAME'].replace("-", "_")
        datalake_response = tenant_service_client.configure_snowflake_data_lake(account_identifier, username, database, warehouse, role, private_key, password)
        del account_identifier, username, password, role, warehouse, database, private_key
        if not datalake_response:
            raise RuntimeError("Failed to configure snowflake datalake in tenant")
        logger.info("Snowflake datalake configured successfully!")

        logger.info("Waiting for pod to be ready")
        pod = wait_for_pod(kube_client, "iris", "databee-api-")
        if not pod:
            raise TimeoutError("Pod creation process timed out")

        logger.info("Generating demo users")
        command_output = kube_client.execute_command_in_pod("iris", pod.metadata.name, [f'export DEMO_TENANT="{os.environ["TENANT_NAME"]}"', 'python manage.py generate_demo_data'])
        logger.info(command_output[1])
        
        logger.info("Cypress tests environment configuration process completed successfully")
        
    except Exception as e:
        logger.error(f"Error during cypress-test environment configuration process: {str(e)}")
        sys.exit(1)